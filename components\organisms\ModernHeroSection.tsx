'use client';

import React from 'react';
import Link from 'next/link';

const ModernHeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-blue-50">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Main Content Container */}
      <div className="container-custom relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          
          {/* Professional Hero Content */}
          <div className="text-center lg:text-left">
            {/* Trust Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white border-2 border-primary-400 text-primary-700 rounded-full text-sm font-bold mb-8 shadow-md">
              <span className="mr-2">🚚</span>
              Transport Falas mbi 5000 Lekë
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-primary font-bold mb-8 leading-tight">
              <span className="text-primary-600">Dashuria</span> për{' '}
              <span className="text-blue-600">Kafshët</span>{' '}
              <span className="text-neutral-800">Fillon Këtu</span>
            </h1>

            <p className="text-lg sm:text-xl text-neutral-600 mb-10 leading-relaxed max-w-2xl">
              Dyqani më i besuar online për produkte të kafshëve në Shqipëri.
              Gjej çdo gjë që i duhet mikut tënd me katër këmbë - nga ushqimi më i mirë
              deri tek lodrat më argëtuese dhe aksesorët më të dobishëm.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-10">
              <Link href="/products" className="btn btn-primary btn-xl group">
                <span className="text-lg font-bold">Eksploro Produktet</span>
                <svg className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              <Link href="/about" className="btn btn-secondary btn-xl">
                <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-lg font-bold">Rreth Nesh</span>
              </Link>
            </div>
            
            {/* Professional Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-neutral-200">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">1500+</div>
                <div className="text-sm text-neutral-600 font-medium">Produkte të Cilësisë</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
                <div className="text-sm text-neutral-600 font-medium">Mbështetje Ekspert</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-600 mb-2">5000+</div>
                <div className="text-sm text-neutral-600 font-medium">Klientë të Kënaqur</div>
              </div>
            </div>
          </div>
          
          {/* Professional Hero Image */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-primary-50 via-white to-blue-50 rounded-2xl p-8 shadow-lg border border-primary-200">
              {/* Clean Pet Icons Display */}
              <div className="relative h-80 flex items-center justify-center">
                <div className="grid grid-cols-2 gap-8 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-bold text-primary-600">DOG</span>
                    </div>
                    <span className="text-sm font-medium text-neutral-600">Qen</span>
                  </div>
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-bold text-blue-600">CAT</span>
                    </div>
                    <span className="text-sm font-medium text-neutral-600">Mace</span>
                  </div>
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-bold text-teal-600">BIRD</span>
                    </div>
                    <span className="text-sm font-medium text-neutral-600">Zogj</span>
                  </div>
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-20 h-20 bg-cyan-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-bold text-cyan-600">FISH</span>
                    </div>
                    <span className="text-sm font-medium text-neutral-600">Peshq</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
