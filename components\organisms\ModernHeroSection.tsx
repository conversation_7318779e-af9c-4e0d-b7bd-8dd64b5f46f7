'use client';

import React from 'react';
import Link from 'next/link';

const ModernHeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
               position: 'relative'
             }}>
      {/* Premium Animated Background Pattern from design.txt */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 animate-float-pattern"
             style={{
               backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='20' cy='20' r='2' fill='rgba(255,224,3,0.1)'/%3E%3Ccircle cx='80' cy='40' r='1.5' fill='rgba(255,224,3,0.15)'/%3E%3Ccircle cx='40' cy='80' r='1' fill='rgba(255,224,3,0.1)'/%3E%3C/svg%3E")`,
               backgroundRepeat: 'repeat'
             }} />
      </div>

      {/* Main Content Container */}
      <div className="container-custom relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          
          {/* Professional Hero Content */}
          <div className="text-center lg:text-left">
            {/* Premium Trust Badge with Glass Effect */}
            <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm border-2 border-white/30 text-white rounded-full text-sm font-bold mb-8 shadow-md">
              <span className="mr-2">🌟</span>
              Featured Today
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-primary font-bold mb-8 leading-tight text-white">
              Premium{' '}
              <span style={{
                background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}>
                Pet Care
              </span>{' '}
              Products
            </h1>

            <p className="text-lg sm:text-xl text-white/80 mb-10 leading-relaxed max-w-2xl">
              Zbuloni koleksionin tonë të kujdesshëm të furnizimeve të cilësisë së lartë për kafshë,
              lodrave dhe aksesorëve të dizajnuara për t'i mbajtur miqtë tuaj me gëzof të lumtur dhe të shëndetshëm.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-10">
              <button className="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold rounded-full transition-all duration-300"
                      style={{
                        background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                        color: '#2d3436',
                        border: 'none',
                        boxShadow: '0 10px 30px rgba(255, 224, 3, 0.3)',
                        transform: 'perspective(1000px) rotateX(0deg)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'perspective(1000px) rotateX(-15deg) translateY(-8px)';
                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(255, 224, 3, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                        e.currentTarget.style.boxShadow = '0 10px 30px rgba(255, 224, 3, 0.3)';
                      }}>
                <span>Shop Now</span>
              </button>
            </div>
            
            {/* Professional Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-neutral-200">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">1500+</div>
                <div className="text-sm text-neutral-600 font-medium">Produkte të Cilësisë</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
                <div className="text-sm text-neutral-600 font-medium">Mbështetje Ekspert</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-600 mb-2">5000+</div>
                <div className="text-sm text-neutral-600 font-medium">Klientë të Kënaqur</div>
              </div>
            </div>
          </div>
          
          {/* Premium Floating Card from design.txt */}
          <div className="relative flex justify-center items-center">
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-3xl p-8 animate-float-card"
                   style={{
                     transform: 'perspective(1000px) rotateY(-15deg) rotateX(10deg)',
                     boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)'
                   }}>
                <h3 className="text-2xl font-bold mb-4"
                    style={{
                      background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}>
                  🌟 Featured Today
                </h3>
                <p className="text-white/90 leading-relaxed text-lg">
                  Premium organic pet food with 30% off for new customers.
                  Give your pets the nutrition they deserve.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
