'use client';

import React, { useState } from 'react';
import ProductCard from '@/components/molecules/ProductCard';

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  count: number;
}

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
}

const CategoryShowcase: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('dogs');
  
  const categories: Category[] = [
    { id: 'dogs', name: 'Qen', icon: '🐕', color: '#FF6B6B', count: 500 },
    { id: 'cats', name: 'Mace', icon: '🐈', color: '#4ECDC4', count: 450 },
    { id: 'birds', name: '<PERSON>og<PERSON>', icon: '🦜', color: '#FFE66D', count: 200 },
    { id: 'fish', name: 'Peshq', icon: '🐠', color: '#A8E6CF', count: 300 },
    { id: 'small', name: '<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>', icon: '🐹', color: '#FFAAA5', count: 150 }
  ];
  
  // Mock products data
  const products: Product[] = [
    {
      id: '1',
      name: 'Ushqim për Qen - Royal Canin 15kg',
      price: 2500,
      image: '/images/placeholder.svg',
      rating: 4.5,
      reviewCount: 23,
      category: 'Ushqim për Qen'
    },
    {
      id: '2',
      name: 'Shtrat për Mace - ComfyBed',
      price: 1200,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 42,
      category: 'Shtrat për Mace'
    },
    {
      id: '3',
      name: 'Akullar për Peshq - 50L',
      price: 8000,
      image: '/images/placeholder.svg',
      rating: 4.2,
      reviewCount: 18,
      category: 'Akullar për Peshq'
    },
    {
      id: '4',
      name: 'Kafaz për Zogj - SpaciousHome',
      price: 3500,
      image: '/images/placeholder.svg',
      rating: 4.6,
      reviewCount: 31,
      category: 'Kafaz për Zogj'
    }
  ];

  return (
    <section className="category-showcase py-16 bg-neutral-white">
      <div className="container mx-auto px-4">
        <h2 className="section-title text-3xl font-bold text-neutral-dark mb-12">
          <span className="title-decorator">✨</span>
          Eksploro sipas Kafshës
          <span className="title-decorator">✨</span>
        </h2>
        
        {/* 3D Category Cards */}
        <div className="category-grid grid grid-cols-2 md:grid-cols-5 gap-4 mb-16">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`category-card rounded-2xl p-6 cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                activeCategory === category.id 
                  ? 'bg-white shadow-xl border-2 border-primary-coral' 
                  : 'bg-white shadow-md'
              }`}
              onClick={() => setActiveCategory(category.id)}
              style={{ 
                borderLeft: `4px solid ${category.color}`,
                '--category-color': category.color
              } as React.CSSProperties}
            >
              <div className="card-3d-wrapper">
                <div className="card-3d-content text-center">
                  <div className="category-icon text-4xl mb-3">{category.icon}</div>
                  <h3 className="font-bold text-neutral-dark mb-2">{category.name}</h3>
                  <div className="category-stats text-sm text-neutral-gray">
                    <span>{category.count}+ produkte</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Featured Products Carousel */}
        <div className="featured-products">
          <h3 className="text-2xl font-bold text-neutral-dark mb-6">
            Produktet e Rekomanduara për {categories.find(c => c.id === activeCategory)?.name}
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <ProductCard 
                key={product.id} 
                product={product} 
                onAddToCart={(product) => console.log('Added to cart:', product)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CategoryShowcase;
