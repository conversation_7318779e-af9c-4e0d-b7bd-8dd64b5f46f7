'use client';

import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/Button';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-neutral-white flex items-center justify-center">
      <div className="text-center max-w-md px-4">
        <div className="text-8xl font-bold text-primary-coral mb-4">404</div>
        <h1 className="text-3xl font-bold text-neutral-dark mb-4">Faqja nuk u gjet!</h1>
        <p className="text-neutral-gray mb-8">
          Na vjen keq, por faqja që po kërkon nuk ekziston ose është zhvendosur.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="primary" 
            size="lg" 
            onClick={() => router.push('/')}
          >
            Kthe<PERSON> në Ballinë
          </Button>
          <Button 
            variant="outline" 
            size="lg" 
            onClick={() => router.push('/products')}
          >
            Shfleto Produktet
          </Button>
        </div>
      </div>
    </div>
  );
}
