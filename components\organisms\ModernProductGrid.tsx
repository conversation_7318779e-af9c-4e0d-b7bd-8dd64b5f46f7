'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  badge?: string;
  isNew?: boolean;
  isOnSale?: boolean;
}

const ModernProductGrid: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const products: Product[] = [
    {
      id: '1',
      name: 'Ushqim Premium për Qen - Royal Canin 15kg',
      price: 2200,
      originalPrice: 2500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 156,
      category: 'Ushqim për Qen',
      badge: 'Bestseller',
      isOnSale: true
    },
    {
      id: '2',
      name: 'Lodër Interaktive për Mace - SmartToy',
      price: 850,
      image: '/images/placeholder.svg',
      rating: 4.6,
      reviewCount: 89,
      category: 'Lodra për Mace',
      isNew: true
    },
    {
      id: '3',
      name: 'Akuarium Modern 100L me LED',
      price: 12000,
      originalPrice: 14000,
      image: '/images/placeholder.svg',
      rating: 4.9,
      reviewCount: 45,
      category: 'Akuarium',
      isOnSale: true
    },
    {
      id: '4',
      name: 'Kafaz Luksoz për Zogj - Villa Bird',
      price: 4500,
      image: '/images/placeholder.svg',
      rating: 4.7,
      reviewCount: 67,
      category: 'Kafaze për Zogj',
      badge: 'Premium'
    },
    {
      id: '5',
      name: 'Set Aksesorësh për Qen - Complete Care',
      price: 1800,
      originalPrice: 2200,
      image: '/images/placeholder.svg',
      rating: 4.5,
      reviewCount: 123,
      category: 'Aksesorë për Qen',
      isOnSale: true
    },
    {
      id: '6',
      name: 'Shtrat Ortopedik për Mace - ComfortMax',
      price: 1500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 78,
      category: 'Shtretër për Mace',
      isNew: true
    },
    {
      id: '7',
      name: 'Ushqim Organik për Zogj - NaturalSeeds',
      price: 650,
      image: '/images/placeholder.svg',
      rating: 4.4,
      reviewCount: 34,
      category: 'Ushqim për Zogj'
    },
    {
      id: '8',
      name: 'Sistem Filtrimi për Akuarium - AquaClean',
      price: 3200,
      originalPrice: 3800,
      image: '/images/placeholder.svg',
      rating: 4.6,
      reviewCount: 52,
      category: 'Aksesorë Akuarium',
      isOnSale: true
    }
  ];

  const categories = [
    { id: 'all', name: 'Të Gjitha', count: products.length },
    { id: 'qen', name: 'Qen', count: products.filter(p => p.category.includes('Qen')).length },
    { id: 'mace', name: 'Mace', count: products.filter(p => p.category.includes('Mace')).length },
    { id: 'zogj', name: 'Zogj', count: products.filter(p => p.category.includes('Zogj')).length },
    { id: 'peshq', name: 'Peshq/Akuarium', count: products.filter(p => p.category.includes('Akuarium')).length },
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sq-AL', {
      style: 'currency',
      currency: 'ALL',
      minimumFractionDigits: 0
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-sm ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-neutral-300'}`}>
        ★
      </span>
    ));
  };

  return (
    <div className="space-y-8">
      {/* Professional Filters and Sort */}
      <div className="bg-white rounded-lg p-6 shadow-md border border-neutral-200">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          {/* Category Filters */}
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-primary-400 text-neutral-900 shadow-md'
                    : 'bg-neutral-100 text-neutral-600 hover:bg-primary-50 hover:text-primary-600'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>

          {/* Sort Options */}
          <div className="flex items-center gap-4">
            <span className="text-neutral-600 text-sm font-medium">Rendit sipas:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 bg-white"
            >
              <option value="newest">Të Rejat</option>
              <option value="price-low">Çmimi: I Ulët → I Lartë</option>
              <option value="price-high">Çmimi: I Lartë → I Ulët</option>
              <option value="rating">Vlerësimi</option>
              <option value="popular">Më të Populluara</option>
            </select>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {products.map((product, index) => (
          <div
            key={product.id}
            className="card card-hover group animate-fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Product Image */}
            <div className="relative overflow-hidden">
              <div className="aspect-square bg-gradient-to-br from-neutral-100 to-neutral-200 flex items-center justify-center">
                <div className="text-6xl opacity-50">📦</div>
              </div>
              
              {/* Professional Badges */}
              <div className="absolute top-4 left-4 flex flex-col gap-2">
                {product.isNew && (
                  <span className="px-3 py-1 bg-primary-400 text-neutral-900 text-xs font-bold rounded-full shadow-md">
                    🆕 E Re
                  </span>
                )}
                {product.isOnSale && (
                  <span className="px-3 py-1 bg-red-500 text-white text-xs font-bold rounded-full shadow-md">
                    🔥 Ofertë
                  </span>
                )}
                {product.badge && (
                  <span className="px-3 py-1 bg-blue-600 text-white text-xs font-bold rounded-full shadow-md">
                    ⭐ {product.badge}
                  </span>
                )}
              </div>

              {/* Professional Quick Actions */}
              <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                <button className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-red-50 hover:text-red-500 transition-colors">
                  <svg className="w-5 h-5 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>
                <Link href={`/products/${product.id}`} className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-50 hover:text-blue-600 transition-colors">
                  <svg className="w-5 h-5 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </Link>
              </div>
            </div>

            <div className="card-body">
              <div className="text-sm text-neutral-500 mb-2">{product.category}</div>
              <h3 className="font-primary font-semibold text-neutral-800 mb-3 group-hover:text-emerald-600 transition-colors line-clamp-2">
                {product.name}
              </h3>
              
              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex">{renderStars(product.rating)}</div>
                <span className="text-sm text-neutral-500">({product.reviewCount})</span>
              </div>

              {/* Price */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-neutral-800">
                    {formatPrice(product.price)}
                  </span>
                  {product.originalPrice && (
                    <span className="text-sm text-neutral-500 line-through">
                      {formatPrice(product.originalPrice)}
                    </span>
                  )}
                </div>
                {product.originalPrice && (
                  <span className="text-sm font-medium text-red-600">
                    -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                  </span>
                )}
              </div>

              {/* Add to Cart Button */}
              <button className="btn btn-primary w-full">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
                Shto në Shportë
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-12">
        <nav className="flex items-center space-x-2">
          <button className="btn btn-outline btn-sm">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button className="btn btn-primary btn-sm">1</button>
          <button className="btn btn-outline btn-sm">2</button>
          <button className="btn btn-outline btn-sm">3</button>
          <span className="text-neutral-400 px-2">...</span>
          <button className="btn btn-outline btn-sm">10</button>
          <button className="btn btn-outline btn-sm">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  );
};

export default ModernProductGrid;
