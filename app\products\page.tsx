import type { Metadata } from 'next';
import ModernHeader from '@/components/organisms/ModernHeader';
import ModernProductGrid from '@/components/organisms/ModernProductGrid';

export const metadata: Metadata = {
  title: 'Produktet - Gjej <PERSON>do <PERSON>jë për Kafshën Tënde',
  description: 'Eksploro koleksionin tonë të gjerë të produkteve për kafshë. Ushqim, lodra, aksesorë dhe shumë më tepër për qen, mace, zogj dhe kafshë të tjera.',
  keywords: ['produktet për kafshë', 'ushqim kafsh<PERSON>sh', 'aks<PERSON><PERSON><PERSON> kafsh<PERSON>', 'lo<PERSON> kafsh<PERSON>'],
};

export default function ProductsPage() {
  return (
    <div className="min-h-screen bg-neutral-50">
      <ModernHeader />

      {/* Professional Hero Section */}
      <section className="pt-24 pb-12 bg-gradient-to-br from-primary-50 to-blue-50">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl lg:text-5xl font-primary font-bold mb-6">
              Gjej <span className="text-primary-600">Çdo Gjë</span> për{' '}
              <span className="text-blue-600">Kafshën Tënde</span>
            </h1>
            <p className="text-xl text-neutral-600 leading-relaxed mb-8">
              Më shumë se 1500 produkte të kualitetit të lartë për të gjitha llojet e kafshëve.
              Nga ushqimi më i mirë deri tek lodrat më argëtuese.
            </p>

            {/* Enhanced Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Kërko produktet..."
                  className="w-full px-6 py-4 pl-12 pr-16 rounded-lg border-2 border-neutral-200 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 text-lg shadow-sm hover:shadow-md transition-all"
                />
                <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-400 hover:bg-primary-500 text-neutral-900 px-4 py-2 rounded-md transition-colors font-medium">
                  Kërko
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="section-padding">
        <div className="container-custom">
          <ModernProductGrid />
        </div>
      </section>
    </div>
  );
}
