'use client';

import { useEffect } from 'react';

const PremiumInteractions: React.FC = () => {
  useEffect(() => {
    // Smooth scrolling for navigation links (from design.txt)
    const handleSmoothScroll = () => {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href') as string);
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth'
            });
          }
        });
      });
    };

    // Header scroll effect (from design.txt)
    const handleHeaderScroll = () => {
      const header = document.querySelector('header');
      if (header) {
        const handleScroll = () => {
          if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 5px 30px rgba(0, 0, 0, 0.1)';
          } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = 'none';
          }
        };
        
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
      }
    };

    // Add to cart animation (from design.txt)
    const handleAddToCartAnimations = () => {
      const addToCartBtns = document.querySelectorAll('[data-add-to-cart]');
      
      addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          // Add success animation
          const originalText = this.textContent;
          const originalStyle = this.style.cssText;
          
          this.style.transform = 'scale(0.95)';
          this.textContent = 'Added!';
          this.style.background = 'linear-gradient(135deg, #00b894 0%, #00a085 100%)';
          
          setTimeout(() => {
            this.style.transform = 'scale(1)';
            this.textContent = originalText;
            this.style.cssText = originalStyle;
          }, 1000);
        });
      });
    };

    // Intersection Observer for fade-in animations
    const handleIntersectionAnimations = () => {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
            observer.unobserve(entry.target);
          }
        });
      }, observerOptions);

      // Observe elements that should animate in
      document.querySelectorAll('.card, .feature-card, .product-card').forEach(el => {
        observer.observe(el);
      });

      return () => observer.disconnect();
    };

    // Parallax effect for background elements
    const handleParallaxEffect = () => {
      const parallaxElements = document.querySelectorAll('[data-parallax]');
      
      const handleScroll = () => {
        const scrolled = window.pageYOffset;
        
        parallaxElements.forEach(element => {
          const rate = scrolled * -0.5;
          element.style.transform = `translateY(${rate}px)`;
        });
      };

      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    };

    // 3D tilt effect for cards (inspired by design.txt)
    const handle3DTiltEffect = () => {
      const tiltElements = document.querySelectorAll('.card-hover');
      
      tiltElements.forEach(element => {
        element.addEventListener('mousemove', (e) => {
          const rect = element.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          
          const rotateX = (y - centerY) / 10;
          const rotateY = (centerX - x) / 10;
          
          element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });
        
        element.addEventListener('mouseleave', () => {
          element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        });
      });
    };

    // Initialize all interactions
    handleSmoothScroll();
    const headerCleanup = handleHeaderScroll();
    handleAddToCartAnimations();
    const intersectionCleanup = handleIntersectionAnimations();
    const parallaxCleanup = handleParallaxEffect();
    handle3DTiltEffect();

    // Cleanup function
    return () => {
      if (headerCleanup) headerCleanup();
      if (intersectionCleanup) intersectionCleanup();
      if (parallaxCleanup) parallaxCleanup();
    };
  }, []);

  return null; // This component doesn't render anything
};

export default PremiumInteractions;
