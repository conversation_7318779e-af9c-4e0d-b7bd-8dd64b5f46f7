'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface CartItem {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  quantity: number;
  category: string;
}

const ModernCart: React.FC = () => {
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: '1',
      name: 'Ushqim Premium për Qen - Royal Canin 15kg',
      price: 2200,
      originalPrice: 2500,
      image: '/images/placeholder.svg',
      quantity: 2,
      category: 'Ushqim për Qen'
    },
    {
      id: '2',
      name: 'Lodër Interaktive për Mace - SmartToy',
      price: 850,
      image: '/images/placeholder.svg',
      quantity: 1,
      category: 'Lodra për Mace'
    },
    {
      id: '3',
      name: 'Set Aksesorësh për Qen - Complete Care',
      price: 1800,
      originalPrice: 2200,
      image: '/images/placeholder.svg',
      quantity: 1,
      category: 'Aksesorë për Qen'
    }
  ]);

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity === 0) {
      setCartItems(cartItems.filter(item => item.id !== id));
    } else {
      setCartItems(cartItems.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      ));
    }
  };

  const removeItem = (id: string) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = subtotal > 5000 ? 0 : 500;
  const total = subtotal + shipping;
  const remainingForFreeShipping = Math.max(0, 5000 - subtotal);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sq-AL', {
      style: 'currency',
      currency: 'ALL',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (cartItems.length === 0) {
    return (
      <div className="text-center py-16 animate-fade-in">
        <div className="text-8xl mb-6">🛒</div>
        <h2 className="text-3xl font-primary font-bold text-neutral-800 mb-4">
          Shporta është Bosh
        </h2>
        <p className="text-xl text-neutral-600 mb-8 max-w-md mx-auto">
          Duket se nuk keni shtuar ende asnjë produkt në shportë. Eksploro produktet tona të mrekullueshme!
        </p>
        <Link href="/products" className="btn btn-primary btn-lg">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Eksploro Produktet
        </Link>
      </div>
    );
  }

  return (
    <div className="grid lg:grid-cols-3 gap-8">
      {/* Cart Items */}
      <div className="lg:col-span-2 space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-primary font-bold text-neutral-800">
            Produktet në Shportë ({cartItems.length})
          </h2>
          <button 
            onClick={() => setCartItems([])}
            className="text-neutral-500 hover:text-red-600 transition-colors"
          >
            Zbraz Shportën
          </button>
        </div>

        {/* Free Shipping Progress */}
        {remainingForFreeShipping > 0 && (
          <div className="card animate-fade-in">
            <div className="card-body">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-neutral-700">
                  🚚 Transport Falas
                </span>
                <span className="text-sm text-emerald-600 font-medium">
                  {formatPrice(remainingForFreeShipping)} të mbetur
                </span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(100, (subtotal / 5000) * 100)}%` }}
                ></div>
              </div>
              <p className="text-xs text-neutral-600 mt-2">
                Shto edhe {formatPrice(remainingForFreeShipping)} për transport falas!
              </p>
            </div>
          </div>
        )}

        {/* Cart Items List */}
        <div className="space-y-4">
          {cartItems.map((item, index) => (
            <div 
              key={item.id} 
              className="card card-hover animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="card-body">
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Product Image */}
                  <div className="md:w-32 flex-shrink-0">
                    <div className="aspect-square bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-lg flex items-center justify-center">
                      <div className="text-4xl opacity-50">📦</div>
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:justify-between">
                      <div className="mb-4 md:mb-0">
                        <h3 className="font-primary font-semibold text-neutral-800 mb-2">
                          {item.name}
                        </h3>
                        <p className="text-sm text-neutral-500 mb-3">{item.category}</p>
                        
                        <div className="flex items-center gap-3">
                          <span className="text-2xl font-bold text-neutral-800">
                            {formatPrice(item.price)}
                          </span>
                          {item.originalPrice && (
                            <span className="text-sm text-neutral-500 line-through">
                              {formatPrice(item.originalPrice)}
                            </span>
                          )}
                          {item.originalPrice && (
                            <span className="text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded">
                              -{Math.round(((item.originalPrice - item.price) / item.originalPrice) * 100)}%
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex flex-col items-end gap-4">
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-neutral-600">Sasia:</span>
                          <div className="flex items-center border border-neutral-200 rounded-lg">
                            <button 
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="w-10 h-10 flex items-center justify-center hover:bg-neutral-50 transition-colors"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                              </svg>
                            </button>
                            <span className="w-12 text-center font-semibold">{item.quantity}</span>
                            <button 
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="w-10 h-10 flex items-center justify-center hover:bg-neutral-50 transition-colors"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            </button>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          <span className="text-lg font-bold text-emerald-600">
                            {formatPrice(item.price * item.quantity)}
                          </span>
                          <button 
                            onClick={() => removeItem(item.id)}
                            className="text-neutral-400 hover:text-red-600 transition-colors"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Continue Shopping */}
        <div className="flex justify-start">
          <Link href="/products" className="btn btn-ghost">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Vazhdo Blerjen
          </Link>
        </div>
      </div>

      {/* Order Summary */}
      <div className="lg:col-span-1">
        <div className="card sticky top-24">
          <div className="card-body">
            <h3 className="text-xl font-primary font-semibold mb-6">
              Përmbledhja e Porosisë
            </h3>
            
            <div className="space-y-4 mb-6">
              <div className="flex justify-between">
                <span className="text-neutral-600">Nëntotali</span>
                <span className="font-semibold">{formatPrice(subtotal)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-neutral-600">Transporti</span>
                <span className="font-semibold">
                  {shipping === 0 ? (
                    <span className="text-emerald-600">Falas</span>
                  ) : (
                    formatPrice(shipping)
                  )}
                </span>
              </div>
              
              {shipping === 0 && (
                <div className="bg-emerald-50 text-emerald-700 p-3 rounded-lg text-sm">
                  🎉 Urime! Keni transport falas
                </div>
              )}
              
              <div className="border-t border-neutral-200 pt-4">
                <div className="flex justify-between text-lg font-bold">
                  <span>Totali</span>
                  <span className="text-emerald-600">{formatPrice(total)}</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <Link href="/checkout" className="btn btn-primary btn-lg w-full">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                Vazhdo në Checkout
              </Link>
              
              <button className="btn btn-outline w-full">
                Ruaj për Më Vonë
              </button>
            </div>
            
            <div className="mt-6 pt-6 border-t border-neutral-200">
              <div className="text-center">
                <p className="text-sm text-neutral-600 mb-3">Paguaj me siguri</p>
                <div className="flex justify-center space-x-2">
                  <div className="w-10 h-6 bg-neutral-100 rounded flex items-center justify-center">
                    <span className="text-xs">💳</span>
                  </div>
                  <div className="w-10 h-6 bg-neutral-100 rounded flex items-center justify-center">
                    <span className="text-xs">🏦</span>
                  </div>
                  <div className="w-10 h-6 bg-neutral-100 rounded flex items-center justify-center">
                    <span className="text-xs">💰</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernCart;
