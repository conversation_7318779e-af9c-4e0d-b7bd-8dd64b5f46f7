import React from 'react';
import Button from '@/components/atoms/Button';

const HeroSection: React.FC = () => {
  return (
    <section className="hero-section relative overflow-hidden bg-gradient-to-r from-primary-teal to-primary-coral py-16 md:py-24">
      {/* Animated Background */}
      <div className="hero-background absolute inset-0">
        <div className="parallax-layer layer-3 animated-gradient absolute inset-0 opacity-20" />
      </div>
      
      {/* Hero Content */}
      <div className="hero-content container mx-auto px-4 relative z-10">
        <div className="max-w-2xl">
          <h1 className="hero-title text-4xl md:text-6xl font-bold text-white mb-6">
            <span className="text-gradient block">Dashuria për <PERSON></span>
            <span className="text-outline block mt-2">Filloi Këtu</span>
          </h1>
          
          <p className="hero-subtitle text-xl text-white mb-8 opacity-90">
            Dyqani më i madh online për kafshë n<PERSON> Shqipëri
          </p>
          
          {/* Animated CTA */}
          <div className="hero-cta flex flex-col sm:flex-row gap-4 mb-12">
            <Button variant="primary" size="lg" className="text-lg px-8 py-4">
              Eksploro Produktet
            </Button>
            
            <Button variant="outline" size="lg" className="text-lg px-8 py-4 bg-white bg-opacity-20 border-white text-white hover:bg-white hover:text-primary-coral">
              <span className="mr-2">▶️</span>
              Shiko Videon
            </Button>
          </div>
          
          {/* Trust Badges */}
          <div className="trust-badges flex flex-wrap gap-6">
            <div className="badge flex items-center text-white">
              <span className="badge-icon mr-2">🚚</span>
              <span>Transport Falas mbi 5,000 Lekë</span>
            </div>
            <div className="badge flex items-center text-white">
              <span className="badge-icon mr-2">🛡️</span>
              <span>Pagesa 100% e Sigurt</span>
            </div>
            <div className="badge flex items-center text-white">
              <span className="badge-icon mr-2">❤️</span>
              <span>20,000+ Klientë të Lumtur</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating Pet Icons */}
      <div className="floating-pets absolute right-0 top-1/2 transform -translate-y-1/2 w-1/3 h-full hidden md:block">
        {['🐕', '🐈', '🐦', '🐠', '🐢'].map((pet, i) => (
          <div 
            key={i} 
            className="floating-pet absolute text-4xl animate-bounce"
            style={{ 
              top: `${20 + i * 15}%`,
              right: `${10 + (i % 3) * 10}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: '3s'
            }}
          >
            {pet}
          </div>
        ))}
      </div>
    </section>
  );
};

export default HeroSection;
