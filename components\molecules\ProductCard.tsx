'use client';

import React, { useState } from 'react';

interface Product {
  id: string;
  name: string;
  price: number;
  oldPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isNew?: boolean;
  discount?: number;
}

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [isLiked, setIsLiked] = useState(false);

  return (
    <div className="card card-hover group">
      {/* Product Badge */}
      {product.isNew && (
        <div className="absolute top-3 left-3 bg-primary-400 text-neutral-900 text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md">
          🆕 E Re
        </div>
      )}

      {product.discount && (
        <div className="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md animate-pulse">
          -{product.discount}%
        </div>
      )}
      
      {/* Product Image */}
      <div className="relative overflow-hidden">
        <div className="aspect-square bg-gradient-to-br from-neutral-100 to-neutral-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
          <div className="text-6xl opacity-50 group-hover:opacity-70 transition-opacity">📦</div>
        </div>

        {/* Quick Actions */}
        <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
          <button
            className={`w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-110 ${isLiked ? 'bg-red-500 text-white' : 'bg-white text-neutral-600 hover:bg-red-50 hover:text-red-500'}`}
            onClick={() => setIsLiked(!isLiked)}
          >
            <svg className="w-5 h-5" fill={isLiked ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>
          <button className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-50 hover:scale-110 transition-all text-neutral-600 hover:text-blue-600">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
          <button className="w-10 h-10 bg-primary-400 rounded-full shadow-lg flex items-center justify-center hover:bg-primary-500 hover:scale-110 transition-all text-neutral-900">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Product Info */}
      <div className="card-body">
        <div className="text-xs text-neutral-500 mb-2 font-medium uppercase tracking-wide">
          {product.category}
        </div>
        <h3 className="font-semibold text-neutral-800 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2 text-lg">
          {product.name}
        </h3>

        {/* Rating */}
        <div className="flex items-center gap-2 mb-4">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <span
                key={i}
                className={`text-sm ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-neutral-300'}`}
              >
                ★
              </span>
            ))}
          </div>
          <span className="text-xs text-neutral-500 font-medium">
            ({product.reviewCount})
          </span>
        </div>

        {/* Price */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold text-neutral-800">
                {product.price} Lekë
              </span>
              {product.oldPrice && (
                <span className="text-sm text-neutral-500 line-through">
                  {product.oldPrice} Lekë
                </span>
              )}
            </div>
            {product.oldPrice && (
              <span className="text-xs text-green-600 font-medium mt-1">
                Kursim: {product.oldPrice - product.price} Lekë
              </span>
            )}
          </div>
          {product.oldPrice && (
            <span className="text-sm font-bold text-red-600 bg-red-50 px-2 py-1 rounded-full">
              -{Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100)}%
            </span>
          )}
        </div>

        {/* Add to Cart Button */}
        <button className="btn btn-primary w-full group-hover:scale-105 transition-transform">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
          </svg>
          Shto në Shportë
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
