'use client';

import React from 'react';

const PremiumFeaturesSection: React.FC = () => {
  const features = [
    {
      icon: '🚚',
      title: 'Free Shipping',
      description: 'Free delivery on orders over $50. Fast, reliable shipping to your doorstep within 2-3 business days.',
      color: 'primary'
    },
    {
      icon: '🏆',
      title: 'Premium Quality',
      description: 'All products are carefully selected and tested to ensure the highest quality standards for your pets.',
      color: 'blue'
    },
    {
      icon: '💝',
      title: 'Satisfaction Guarantee',
      description: '30-day money-back guarantee. If you\'re not satisfied, we\'ll make it right or refund your purchase.',
      color: 'teal'
    },
    {
      icon: '🩺',
      title: 'Vet Approved',
      description: 'Our products are recommended by veterinarians and pet care professionals across the country.',
      color: 'green'
    }
  ];

  return (
    <section className="relative overflow-hidden py-20"
             style={{
               background: 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)'
             }}>
      {/* Animated Background Pattern from design.txt */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 animate-rotate"
             style={{
               backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cpolygon points='100,10 40,180 190,60 10,60 160,180' fill='rgba(255,224,3,0.03)'/%3E%3C/svg%3E")`,
               backgroundRepeat: 'repeat'
             }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-primary font-bold text-white mb-8">
            Why Choose{' '}
            <span style={{
              background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              PetShop AL
            </span>
            ?
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative"
            >
              {/* Premium Glass Card with 3D Effects from design.txt */}
              <div className="bg-white/10 backdrop-blur-md border border-white/10 rounded-3xl p-8 text-center transition-all duration-300 h-full"
                   style={{
                     transform: 'perspective(1000px) rotateX(0deg)'
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.transform = 'perspective(1000px) rotateX(-10deg) translateY(-10px)';
                     e.currentTarget.style.background = 'rgba(255, 224, 3, 0.1)';
                     e.currentTarget.style.borderColor = 'rgba(255, 224, 3, 0.3)';
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                     e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                     e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                   }}>
                
                {/* Premium Icon with Gradient Background */}
                <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl transition-all duration-300 group-hover:scale-110"
                     style={{
                       background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                       boxShadow: '0 10px 30px rgba(255, 224, 3, 0.3)'
                     }}>
                  {feature.icon}
                </div>

                <h3 className="text-xl font-primary font-bold text-white mb-4 group-hover:text-primary-300 transition-colors">
                  {feature.title}
                </h3>

                <p className="text-white/80 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PremiumFeaturesSection;
