'use client';

import React, { useState, useRef, useEffect } from 'react';

interface SearchSuggestion {
  id: string;
  name: string;
  category: string;
  type: 'product' | 'category';
}

const EnhancedSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);

  // Mock suggestions data
  const mockSuggestions: SearchSuggestion[] = [
    { id: '1', name: 'Ushqim për Qen Royal Canin', category: 'Ushqim për Qen', type: 'product' },
    { id: '2', name: '<PERSON>dra për Mace', category: 'Lodra', type: 'category' },
    { id: '3', name: '<PERSON>ku<PERSON> 100L', category: 'Akuarium', type: 'product' },
    { id: '4', name: '<PERSON><PERSON><PERSON><PERSON>', category: '<PERSON><PERSON><PERSON>', type: 'category' },
    { id: '5', name: 'Ushqim Organik për Mace', category: 'Ushqim për Mace', type: 'product' },
    { id: '6', name: 'Aksesorë për Qen', category: 'Aksesorë', type: 'category' },
    { id: '7', name: 'Vitamina për Kafshë', category: 'Shëndet', type: 'product' },
    { id: '8', name: 'Shtretër për Qen', category: 'Shtretër', type: 'product' },
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    if (value.length > 1) {
      const filtered = mockSuggestions.filter(
        suggestion =>
          suggestion.name.toLowerCase().includes(value.toLowerCase()) ||
          suggestion.category.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 6));
      setIsOpen(true);
      setSelectedIndex(-1);
    } else {
      setSuggestions([]);
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.name);
    setIsOpen(false);
    setSelectedIndex(-1);
    // Navigate to product or category page
    console.log('Navigate to:', suggestion);
  };

  const handleSearch = () => {
    if (query.trim()) {
      setIsOpen(false);
      // Perform search
      console.log('Search for:', query);
    }
  };

  const getIconForType = (type: string) => {
    return type === 'product' ? '📦' : '📂';
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-2xl">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length > 1 && setIsOpen(true)}
          placeholder="Kërko produktet për kafshën tënde..."
          className="w-full px-4 py-3 pl-12 pr-20 rounded-xl border-2 border-neutral-200 focus:border-yellow-400 focus:outline-none transition-all duration-300 bg-white shadow-sm hover:shadow-md text-lg"
        />
        
        {/* Search Icon */}
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          <svg className="w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        
        {/* Search Button */}
        <button 
          onClick={handleSearch}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-primary btn-sm"
        >
          Kërko
        </button>
        
        {/* Clear Button */}
        {query && (
          <button
            onClick={() => {
              setQuery('');
              setSuggestions([]);
              setIsOpen(false);
            }}
            className="absolute right-20 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-neutral-200 hover:bg-neutral-300 flex items-center justify-center transition-colors"
          >
            <svg className="w-4 h-4 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-yellow-200 rounded-xl shadow-xl z-50 overflow-hidden">
          <div className="py-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`w-full px-4 py-3 text-left hover:bg-yellow-50 transition-colors flex items-center gap-3 ${
                  index === selectedIndex ? 'bg-yellow-100 border-l-4 border-yellow-400' : ''
                }`}
              >
                <span className="text-2xl">{getIconForType(suggestion.type)}</span>
                <div className="flex-1">
                  <div className="font-medium text-neutral-800">{suggestion.name}</div>
                  <div className="text-sm text-neutral-500">{suggestion.category}</div>
                </div>
                <div className="text-xs text-neutral-400 uppercase font-medium">
                  {suggestion.type === 'product' ? 'Produkt' : 'Kategori'}
                </div>
              </button>
            ))}
          </div>
          
          {/* Quick Categories */}
          <div className="border-t border-neutral-200 p-4 bg-neutral-50">
            <div className="text-sm font-medium text-neutral-600 mb-2">Kategori të Populluara:</div>
            <div className="flex flex-wrap gap-2">
              {['Qen', 'Mace', 'Zogj', 'Peshq'].map((category) => (
                <button
                  key={category}
                  className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium hover:bg-yellow-200 transition-colors"
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* No Results */}
      {isOpen && query.length > 1 && suggestions.length === 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-neutral-200 rounded-xl shadow-xl z-50 p-6 text-center">
          <div className="text-4xl mb-2">🔍</div>
          <div className="font-medium text-neutral-800 mb-1">Nuk u gjetën rezultate</div>
          <div className="text-sm text-neutral-500">Provo me fjalë kyçe të tjera ose shiko kategoritë tona</div>
        </div>
      )}
    </div>
  );
};

export default EnhancedSearch;
