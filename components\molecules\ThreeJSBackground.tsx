'use client';

import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

const ThreeJSBackground: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.z = 5;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      alpha: true, 
      antialias: true 
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    rendererRef.current = renderer;
    mountRef.current.appendChild(renderer.domElement);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);

    // Create floating pet-related objects
    const objects: THREE.Mesh[] = [];

    // Dog bone
    const boneGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1, 8);
    const boneMaterial = new THREE.MeshLambertMaterial({ color: 0xffe003 });
    const bone = new THREE.Mesh(boneGeometry, boneMaterial);
    bone.position.set(-3, 2, 0);
    scene.add(bone);
    objects.push(bone);

    // Cat toy ball
    const ballGeometry = new THREE.SphereGeometry(0.3, 16, 16);
    const ballMaterial = new THREE.MeshLambertMaterial({ color: 0xff6b6b });
    const ball = new THREE.Mesh(ballGeometry, ballMaterial);
    ball.position.set(3, -1, 0);
    scene.add(ball);
    objects.push(ball);

    // Fish bowl
    const bowlGeometry = new THREE.SphereGeometry(0.4, 16, 16, 0, Math.PI * 2, 0, Math.PI / 2);
    const bowlMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x4ecdc4, 
      transparent: true, 
      opacity: 0.7 
    });
    const bowl = new THREE.Mesh(bowlGeometry, bowlMaterial);
    bowl.position.set(0, -2, 0);
    scene.add(bowl);
    objects.push(bowl);

    // Bird cage (simplified as wireframe box)
    const cageGeometry = new THREE.BoxGeometry(0.8, 1, 0.8);
    const cageMaterial = new THREE.MeshBasicMaterial({ 
      color: 0x8b5cf6, 
      wireframe: true 
    });
    const cage = new THREE.Mesh(cageGeometry, cageMaterial);
    cage.position.set(-2, -1, 0);
    scene.add(cage);
    objects.push(cage);

    // Paw prints (using small spheres)
    for (let i = 0; i < 10; i++) {
      const pawGeometry = new THREE.SphereGeometry(0.05, 8, 8);
      const pawMaterial = new THREE.MeshLambertMaterial({ 
        color: 0xffe003,
        transparent: true,
        opacity: 0.6
      });
      const paw = new THREE.Mesh(pawGeometry, pawMaterial);
      paw.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 6,
        (Math.random() - 0.5) * 4
      );
      scene.add(paw);
      objects.push(paw);
    }

    // Floating hearts
    for (let i = 0; i < 5; i++) {
      const heartGeometry = new THREE.SphereGeometry(0.1, 8, 8);
      const heartMaterial = new THREE.MeshLambertMaterial({ 
        color: 0xff69b4,
        transparent: true,
        opacity: 0.8
      });
      const heart = new THREE.Mesh(heartGeometry, heartMaterial);
      heart.position.set(
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 3
      );
      scene.add(heart);
      objects.push(heart);
    }

    // Animation
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);

      // Rotate and float objects
      objects.forEach((object, index) => {
        object.rotation.x += 0.01;
        object.rotation.y += 0.01;
        
        // Floating motion
        object.position.y += Math.sin(Date.now() * 0.001 + index) * 0.002;
        
        // Gentle drift
        object.position.x += Math.sin(Date.now() * 0.0005 + index) * 0.001;
      });

      // Camera gentle movement
      camera.position.x = Math.sin(Date.now() * 0.0003) * 0.5;
      camera.position.y = Math.cos(Date.now() * 0.0002) * 0.3;
      camera.lookAt(scene.position);

      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      if (!camera || !renderer) return;
      
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      
      // Dispose of Three.js objects
      objects.forEach(object => {
        if (object.geometry) object.geometry.dispose();
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });
      
      renderer?.dispose();
    };
  }, []);

  return (
    <div 
      ref={mountRef} 
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default ThreeJSBackground;
