'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import EnhancedSearch from '@/components/molecules/EnhancedSearch';

const ModernHeader: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-primary/20'
        : 'bg-white/95 backdrop-blur-md border-b border-primary/20'
    }`}>
      <div className="container-custom">
        <div className="flex items-center justify-between h-20">
          {/* Premium Logo with Gradient */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 rounded-xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-md"
                 style={{
                   background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                   boxShadow: '0 8px 25px rgba(255, 224, 3, 0.3)'
                 }}>
              <span className="text-neutral-900 font-bold text-2xl">🐾</span>
            </div>
            <div>
              <h1 className="text-2xl font-primary font-bold">
                <span style={{
                  background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 0 30px rgba(255, 224, 3, 0.3)'
                }}>
                  PetShop
                </span>
                <span className="text-neutral-800">AL</span>
              </h1>
              <p className="text-xs text-neutral-500 font-medium">Dashuria për Kafshët</p>
            </div>
          </Link>

          {/* Enhanced Search Bar */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <EnhancedSearch />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link href="/" className="text-neutral-700 hover:text-primary-600 font-medium transition-colors relative group">
              Ballina
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-400 transition-all group-hover:w-full"></span>
            </Link>
            <div className="relative group">
              <Link href="/products" className="text-neutral-700 hover:text-primary-600 font-medium transition-colors flex items-center">
                Produktet
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </Link>
              {/* Dropdown menu would go here */}
            </div>
            <Link href="/about" className="text-neutral-700 hover:text-primary-600 font-medium transition-colors relative group">
              Rreth Nesh
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-400 transition-all group-hover:w-full"></span>
            </Link>
            <Link href="/contact" className="text-neutral-700 hover:text-primary-600 font-medium transition-colors relative group">
              Kontakt
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-400 transition-all group-hover:w-full"></span>
            </Link>
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-3">
            {/* Mobile Search Button */}
            <button className="md:hidden p-2 text-neutral-600 hover:text-primary-600 transition-colors">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>

            {/* Wishlist */}
            <button className="relative p-2 text-neutral-600 hover:text-primary-600 transition-colors group">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-primary-400 text-neutral-900 text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                2
              </span>
            </button>

            {/* User Account */}
            <button className="p-2 text-neutral-600 hover:text-primary-600 transition-colors">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </button>

            {/* Premium Shopping Cart with 3D Effects */}
            <Link href="/cart" className="relative group">
              <button className="relative p-3 text-neutral-600 hover:text-primary-600 transition-all duration-300 rounded-full hover:bg-primary-50"
                      style={{
                        transform: 'perspective(1000px) rotateX(0deg)',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'perspective(1000px) rotateX(-10deg) translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 224, 3, 0.3)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v4.01" />
                </svg>
                <span className="absolute -top-1 -right-1 text-neutral-900 text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse"
                      style={{
                        background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                        boxShadow: '0 4px 14px rgba(255, 224, 3, 0.4)'
                      }}>
                  3
                </span>
              </button>
            </Link>

            {/* Premium CTA Button */}
            <button className="btn btn-primary btn-sm hidden md:inline-flex group"
                    style={{
                      background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)',
                      color: '#2d3436',
                      border: 'none',
                      borderRadius: '50px',
                      fontWeight: '600',
                      boxShadow: '0 8px 25px rgba(255, 224, 3, 0.3)',
                      transform: 'perspective(1000px) rotateX(0deg)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'perspective(1000px) rotateX(-10deg) translateY(-5px)';
                      e.currentTarget.style.boxShadow = '0 15px 35px rgba(255, 224, 3, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) translateY(0px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 224, 3, 0.3)';
                    }}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Llogaria
            </button>
            
            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 text-neutral-600 hover:text-yellow-600 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden py-6 border-t border-neutral-200 bg-white/95 backdrop-blur-md">
            {/* Mobile Search */}
            <div className="px-4 mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Kërko produktet..."
                  className="w-full px-4 py-3 pl-12 rounded-xl border-2 border-neutral-200 focus:border-yellow-400 focus:outline-none transition-colors"
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <nav className="flex flex-col space-y-1 px-4">
              <Link href="/" className="text-neutral-700 hover:text-yellow-600 hover:bg-yellow-50 font-medium py-3 px-4 rounded-lg transition-colors">
                🏠 Ballina
              </Link>
              <Link href="/products" className="text-neutral-700 hover:text-yellow-600 hover:bg-yellow-50 font-medium py-3 px-4 rounded-lg transition-colors">
                🛍️ Produktet
              </Link>
              <Link href="/about" className="text-neutral-700 hover:text-yellow-600 hover:bg-yellow-50 font-medium py-3 px-4 rounded-lg transition-colors">
                ℹ️ Rreth Nesh
              </Link>
              <Link href="/contact" className="text-neutral-700 hover:text-yellow-600 hover:bg-yellow-50 font-medium py-3 px-4 rounded-lg transition-colors">
                📞 Kontakt
              </Link>

              <div className="pt-4 space-y-3">
                <button className="btn btn-primary w-full">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Llogaria Ime
                </button>
                <Link href="/cart" className="btn btn-outline w-full">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                  </svg>
                  Shporta (3)
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default ModernHeader;
