'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

const ModernHeader: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isProductsMenuOpen, setIsProductsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const productCategories = [
    {
      title: 'Qen',
      icon: '🐕',
      items: ['Ushqim për Qen', 'Lodra për Qen', 'Aksesorë për Qen', '<PERSON>jdes Shëndetësor']
    },
    {
      title: 'Mace',
      icon: '🐱',
      items: ['<PERSON>hqim për Mace', '<PERSON><PERSON> për Mace', '<PERSON><PERSON><PERSON><PERSON><PERSON> për <PERSON>', '<PERSON>j<PERSON> Shëndetësor']
    },
    {
      title: 'Zogj',
      icon: '🐦',
      items: ['Ushqim për Zogj', 'Kafaze', 'Aksesorë për Zogj', 'Kujdes Shëndetësor']
    },
    {
      title: 'Peshq',
      icon: '🐠',
      items: ['Ushqim për Peshq', 'Akuarium', 'Aksesorë për Peshq', 'Kujdes Shëndetësor']
    }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg'
        : 'bg-white/95 backdrop-blur-md'
    } border-b border-neutral-100`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Modern Clean Logo */}
          <Link href="/" className="flex items-center group">
            <div className="w-10 h-10 rounded-xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-sm"
                 style={{
                   background: 'linear-gradient(135deg, #ffe003 0%, #ffb300 100%)'
                 }}>
              <span className="text-neutral-900 font-bold text-lg">🐾</span>
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold text-neutral-900">
                PetShop<span className="text-primary-500">AL</span>
              </h1>
            </div>
          </Link>

          {/* Ultra Modern Search Bar */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-neutral-400 group-focus-within:text-primary-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Kërko produktet për kafshët..."
                className="w-full pl-12 pr-24 py-3 bg-neutral-50 border-0 rounded-full text-neutral-900 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:bg-white transition-all duration-300 text-sm"
              />
              <button className="absolute inset-y-0 right-0 pr-2 flex items-center">
                <div className="bg-primary-400 hover:bg-primary-500 text-neutral-900 px-6 py-2 rounded-full text-sm font-semibold transition-all duration-300 shadow-sm hover:shadow-md">
                  Kërko
                </div>
              </button>
            </div>
          </div>

          {/* Modern Navigation with Premium Mega Menu */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link href="/" className="text-neutral-700 hover:text-neutral-900 font-medium transition-colors py-2">
              Ballina
            </Link>

            {/* Premium Products Mega Menu */}
            <div className="relative group">
              <button
                className="flex items-center space-x-1 text-neutral-700 hover:text-neutral-900 font-medium transition-all duration-300 py-2"
                onMouseEnter={() => setIsProductsMenuOpen(true)}
                onMouseLeave={() => setIsProductsMenuOpen(false)}
              >
                <span>Produktet</span>
                <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Ultra Modern Mega Menu */}
              <div
                className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-[700px] bg-white rounded-2xl shadow-2xl border border-neutral-100 transition-all duration-500 z-50 overflow-hidden ${
                  isProductsMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
                }`}
                onMouseEnter={() => setIsProductsMenuOpen(true)}
                onMouseLeave={() => setIsProductsMenuOpen(false)}
              >
                <div className="p-8">
                  <div className="grid grid-cols-3 gap-8">
                    {productCategories.map((category, index) => (
                      <div key={index} className="space-y-4">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center shadow-sm">
                            <span className="text-white text-lg">{category.icon}</span>
                          </div>
                          <h3 className="font-bold text-neutral-900 text-lg">{category.title}</h3>
                        </div>
                        <ul className="space-y-3">
                          {category.items.map((item, itemIndex) => (
                            <li key={itemIndex}>
                              <Link href={`/products/${category.title.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                                    className="text-neutral-600 hover:text-primary-500 transition-colors block py-1 text-sm">
                                {item}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                  <div className="mt-8 pt-6 border-t border-neutral-100">
                    <Link href="/products" className="inline-flex items-center space-x-2 text-primary-500 hover:text-primary-600 font-semibold transition-colors">
                      <span>Shiko të gjitha produktet</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <Link href="/about" className="text-neutral-700 hover:text-neutral-900 font-medium transition-colors py-2">
              Rreth Nesh
            </Link>
            <Link href="/contact" className="text-neutral-700 hover:text-neutral-900 font-medium transition-colors py-2">
              Kontakt
            </Link>
          </nav>

          {/* Modern Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Wishlist */}
            <button className="relative p-3 text-neutral-600 hover:text-red-500 hover:bg-red-50 rounded-xl transition-all duration-300 group hidden sm:flex">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-sm">
                2
              </span>
            </button>

            {/* Modern Shopping Cart */}
            <Link href="/cart" className="relative p-3 text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300 group">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-primary-400 text-neutral-900 text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-sm">
                3
              </span>
            </Link>

            {/* Premium Account Button */}
            <button className="bg-gradient-to-r from-primary-400 to-primary-500 hover:from-primary-500 hover:to-primary-600 text-neutral-900 px-6 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 hidden sm:flex items-center shadow-sm hover:shadow-md">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Llogaria
            </button>

            {/* Modern Mobile Menu Button */}
            <button
              className="lg:hidden p-3 text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100 rounded-xl transition-all duration-300"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Premium Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-neutral-100 bg-white/95 backdrop-blur-md">
            {/* Modern Mobile Search */}
            <div className="p-6">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Kërko produktet për kafshët..."
                  className="w-full pl-12 pr-4 py-3 bg-neutral-50 border-0 rounded-full text-neutral-900 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:bg-white transition-all duration-300"
                />
              </div>
            </div>

            <nav className="px-4 pb-4 space-y-1">
              <Link href="/" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Ballina
              </Link>

              {/* Mobile Products Menu */}
              <div className="space-y-1">
                <div className="px-3 py-2 text-base font-medium text-neutral-700">Produktet</div>
                <div className="pl-6 space-y-1">
                  {productCategories.map((category, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center space-x-2 px-3 py-1 text-sm font-medium text-neutral-600">
                        <span>{category.icon}</span>
                        <span>{category.title}</span>
                      </div>
                      <div className="pl-8 space-y-1">
                        {category.items.map((item, itemIndex) => (
                          <Link key={itemIndex} href={`/products/${category.title.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                                className="block px-3 py-1 text-sm text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                            {item}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Link href="/about" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Rreth Nesh
              </Link>
              <Link href="/contact" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Kontakt
              </Link>

              <div className="pt-6 space-y-3">
                <Link href="/cart" className="flex items-center justify-between w-full px-4 py-3 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300">
                  <span className="flex items-center">
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                    Shporta
                  </span>
                  <span className="bg-primary-400 text-neutral-900 text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-sm">3</span>
                </Link>
                <button className="w-full bg-gradient-to-r from-primary-400 to-primary-500 hover:from-primary-500 hover:to-primary-600 text-neutral-900 px-6 py-3 rounded-xl text-base font-semibold transition-all duration-300 shadow-sm hover:shadow-md">
                  Llogaria Ime
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default ModernHeader;
