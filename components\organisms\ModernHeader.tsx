'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import EnhancedSearch from '@/components/molecules/EnhancedSearch';

const ModernHeader: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isProductsMenuOpen, setIsProductsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const productCategories = [
    {
      title: 'Qen',
      icon: '🐕',
      items: ['Ushqim për Qen', '<PERSON>dra për Qen', '<PERSON>ks<PERSON><PERSON><PERSON> për Qen', '<PERSON><PERSON><PERSON>hëndetësor']
    },
    {
      title: 'Mace',
      icon: '🐱',
      items: ['<PERSON>hqim për Mace', '<PERSON><PERSON> për Mace', '<PERSON>ks<PERSON><PERSON><PERSON> për Mace', '<PERSON>j<PERSON>hëndetësor']
    },
    {
      title: 'Zogj',
      icon: '🐦',
      items: ['Ushqim për Zogj', 'Kafaze', 'Aksesorë për Zogj', 'Kujdes Shëndetësor']
    },
    {
      title: 'Peshq',
      icon: '🐠',
      items: ['Ushqim për Peshq', 'Akuarium', 'Aksesorë për Peshq', 'Kujdes Shëndetësor']
    }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white shadow-lg'
        : 'bg-white shadow-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Improved Logo with Better Alignment */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="w-10 h-10 rounded-lg flex items-center justify-center group-hover:scale-105 transition-all duration-200"
                 style={{
                   background: '#ffe003'
                 }}>
              <span className="text-neutral-900 font-bold text-lg flex items-center justify-center">🐾</span>
            </div>
            <div className="flex flex-col">
              <h1 className="text-xl font-bold text-neutral-800">
                PetShop<span className="text-primary-600">AL</span>
              </h1>
              <p className="text-xs text-neutral-500 hidden sm:block">Dashuria për Kafshët</p>
            </div>
          </Link>

          {/* Improved Search Bar */}
          <div className="hidden md:flex flex-1 max-w-lg mx-6">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="Kërko produktet për kafshët..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <div className="bg-primary-400 text-neutral-900 px-3 py-1 rounded-md text-sm font-medium hover:bg-primary-500 transition-colors">
                  Kërko
                </div>
              </button>
            </div>
          </div>

          {/* Improved Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            <Link href="/" className="px-3 py-2 text-sm font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
              Ballina
            </Link>

            {/* Products Mega Menu */}
            <div className="relative group">
              <button
                className="px-3 py-2 text-sm font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors flex items-center"
                onMouseEnter={() => setIsProductsMenuOpen(true)}
                onMouseLeave={() => setIsProductsMenuOpen(false)}
              >
                Produktet
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Mega Menu Dropdown */}
              <div
                className={`absolute top-full left-0 mt-1 w-96 bg-white rounded-lg shadow-lg border border-neutral-200 transition-all duration-200 ${
                  isProductsMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
                }`}
                onMouseEnter={() => setIsProductsMenuOpen(true)}
                onMouseLeave={() => setIsProductsMenuOpen(false)}
              >
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4">
                    {productCategories.map((category, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center space-x-2 mb-3">
                          <span className="text-2xl">{category.icon}</span>
                          <h3 className="font-semibold text-neutral-800">{category.title}</h3>
                        </div>
                        <ul className="space-y-1">
                          {category.items.map((item, itemIndex) => (
                            <li key={itemIndex}>
                              <Link href={`/products/${category.title.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                                    className="text-sm text-neutral-600 hover:text-primary-600 hover:bg-primary-50 block px-2 py-1 rounded transition-colors">
                                {item}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 pt-4 border-t border-neutral-200">
                    <Link href="/products" className="text-sm font-medium text-primary-600 hover:text-primary-700">
                      Shiko të gjitha produktet →
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <Link href="/about" className="px-3 py-2 text-sm font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
              Rreth Nesh
            </Link>
            <Link href="/contact" className="px-3 py-2 text-sm font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
              Kontakt
            </Link>
          </nav>

          {/* Improved User Actions */}
          <div className="flex items-center space-x-2">
            {/* Wishlist */}
            <button className="relative p-2 text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors group hidden sm:flex">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold text-[10px]">
                2
              </span>
            </button>

            {/* Shopping Cart */}
            <Link href="/cart" className="relative p-2 text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors group">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-primary-400 text-neutral-900 text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold text-[10px]">
                3
              </span>
            </Link>

            {/* User Account Button */}
            <button className="bg-primary-400 text-neutral-900 px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-500 transition-colors hidden sm:flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Llogaria
            </button>

            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Improved Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-neutral-200 bg-white">
            {/* Mobile Search */}
            <div className="p-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Kërko produktet për kafshët..."
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <nav className="px-4 pb-4 space-y-1">
              <Link href="/" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Ballina
              </Link>

              {/* Mobile Products Menu */}
              <div className="space-y-1">
                <div className="px-3 py-2 text-base font-medium text-neutral-700">Produktet</div>
                <div className="pl-6 space-y-1">
                  {productCategories.map((category, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center space-x-2 px-3 py-1 text-sm font-medium text-neutral-600">
                        <span>{category.icon}</span>
                        <span>{category.title}</span>
                      </div>
                      <div className="pl-8 space-y-1">
                        {category.items.map((item, itemIndex) => (
                          <Link key={itemIndex} href={`/products/${category.title.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                                className="block px-3 py-1 text-sm text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                            {item}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Link href="/about" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Rreth Nesh
              </Link>
              <Link href="/contact" className="block px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                Kontakt
              </Link>

              <div className="pt-4 space-y-2">
                <Link href="/cart" className="flex items-center justify-between w-full px-3 py-2 text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors">
                  <span className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                    Shporta
                  </span>
                  <span className="bg-primary-400 text-neutral-900 text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">3</span>
                </Link>
                <button className="w-full bg-primary-400 text-neutral-900 px-4 py-3 rounded-lg text-base font-medium hover:bg-primary-500 transition-colors">
                  Llogaria Ime
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default ModernHeader;
