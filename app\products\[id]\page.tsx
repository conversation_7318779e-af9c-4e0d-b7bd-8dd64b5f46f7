'use client';

import Header from '@/components/organisms/Header';
import Button from '@/components/atoms/Button';

export default function ProductDetailPage() {
  // Mock product data
  const product = {
    id: '1',
    name: 'Ushqim për Qen - Royal Canin 15kg',
    price: 2500,
    oldPrice: 2800,
    image: '/images/dog-food.jpg',
    rating: 4.5,
    reviewCount: 23,
    category: 'Ushqim për Qen',
    discount: 11,
    description: 'Ky ushqim i veçantë për qen është formuluar për të përmbushur të gjitha nevojat ushqimore të qenit tuaj. Përmban proteina të larta dhe yndyrna esenciale për një flok të shëndetshëm dhe një sistem imunitar të fortë.',
    features: [
      'Për qen të rritur',
      'Përmban EPA dhe DHA për shëndetin e zemrës',
      '<PERSON>lok i shëndetshëm dhe të shkëlqyer',
      'Tretës i lehtë'
    ]
  };

  return (
    <div className="min-h-screen bg-neutral-white">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm text-neutral-gray">
          <a href="#" className="hover:text-primary-coral">Ballina</a> / 
          <a href="#" className="hover:text-primary-coral ml-1">Produktet</a> /
          <span className="text-neutral-dark ml-1">{product.category}</span>
        </nav>
        
        {/* Product Detail */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Product Images */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl shadow-md p-6">
              <img 
                src={product.image || '/placeholder.jpg'} 
                alt={product.name} 
                className="w-full h-96 object-contain rounded-lg"
              />
              
              <div className="grid grid-cols-4 gap-4 mt-6">
                {[1, 2, 3, 4].map((img) => (
                  <div key={img} className="border border-neutral-light rounded-lg p-2 cursor-pointer hover:border-primary-coral">
                    <img 
                      src={product.image || '/placeholder.jpg'} 
                      alt={`${product.name} ${img}`} 
                      className="w-full h-20 object-contain"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Product Info */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex justify-between items-start">
                <div>
                  <span className="text-primary-teal font-medium">{product.category}</span>
                  <h1 className="text-2xl font-bold text-neutral-dark mt-1">{product.name}</h1>
                </div>
                
                {product.discount && (
                  <span className="bg-secondary-peach text-neutral-dark text-sm font-bold px-3 py-1 rounded-full">
                    -{product.discount}%
                  </span>
                )}
              </div>
              
              {/* Rating */}
              <div className="flex items-center mt-4">
                <div className="flex text-yellow-400">
                  {'★'.repeat(Math.floor(product.rating))}
                  {'☆'.repeat(5 - Math.floor(product.rating))}
                </div>
                <span className="text-neutral-gray text-sm ml-2">
                  ({product.reviewCount} vlerësime)
                </span>
              </div>
              
              {/* Price */}
              <div className="mt-6">
                <div className="flex items-baseline">
                  {product.oldPrice && (
                    <span className="text-neutral-gray line-through text-lg mr-3">
                      {product.oldPrice} Lekë
                    </span>
                  )}
                  <span className="text-3xl font-bold text-primary-coral">
                    {product.price} <span className="text-lg">Lekë</span>
                  </span>
                </div>
              </div>
              
              {/* Description */}
              <p className="text-neutral-gray mt-6">
                {product.description}
              </p>
              
              {/* Features */}
              <div className="mt-6">
                <h3 className="font-bold text-neutral-dark mb-3">Karakteristikat kryesore:</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-primary-coral mr-2">✓</span>
                      <span className="text-neutral-gray">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* Quantity and Add to Cart */}
              <div className="mt-8 pt-6 border-t border-neutral-light">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <div className="flex items-center">
                    <span className="text-neutral-gray mr-3">Sasia:</span>
                    <div className="flex items-center border border-neutral-light rounded-md">
                      <button className="px-3 py-2 text-neutral-gray hover:text-primary-coral">
                        -
                      </button>
                      <span className="px-4 py-2">1</span>
                      <button className="px-3 py-2 text-neutral-gray hover:text-primary-coral">
                        +
                      </button>
                    </div>
                  </div>
                  
                  <Button variant="primary" size="lg" className="flex-1">
                    Shto në Shportë
                  </Button>
                </div>
              </div>
              
              {/* Wishlist and Share */}
              <div className="mt-6 flex space-x-4">
                <button className="flex items-center text-neutral-gray hover:text-primary-coral">
                  <span className="mr-2">🤍</span>
                  <span>Shto në Listën e Dëshirave</span>
                </button>
                <button className="flex items-center text-neutral-gray hover:text-primary-coral">
                  <span className="mr-2">↗️</span>
                  <span>Shpërndaje</span>
                </button>
              </div>
            </div>
            
            {/* Product Reviews */}
            <div className="bg-white rounded-xl shadow-md p-6 mt-6">
              <h2 className="text-xl font-bold text-neutral-dark mb-6">Vlerësimet e Klientëve</h2>
              
              <div className="space-y-6">
                {[1, 2, 3].map((review) => (
                  <div key={review} className="border-b border-neutral-light pb-6 last:border-0 last:pb-0">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="font-bold text-neutral-dark">User {review}</h4>
                        <div className="flex text-yellow-400 mt-1">
                          {'★'.repeat(5)}
                        </div>
                      </div>
                      <span className="text-neutral-gray text-sm">10 Maj 2025</span>
                    </div>
                    <p className="text-neutral-gray mt-3">
                      Produkt i shkëlqyer! Qeni im e ka të preferuar këtë ushqim. 
                      Paketimi është i mirë dhe çmimi është konkurrues.
                    </p>
                  </div>
                ))}
              </div>
              
              <Button variant="outline" className="w-full mt-6">
                Shkruani një Vlerësim
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
