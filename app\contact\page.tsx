import type { Metadata } from 'next';
import ModernHeader from '@/components/organisms/ModernHeader';

export const metadata: Metadata = {
  title: 'Kontakt - PetShop Albania',
  description: 'Na kontaktoni për çdo pyetje, këshillë apo mbështetje. Ekipi ynë është gati të ju ndihmojë 24/7.',
  keywords: ['kontakt', 'mbështetje', 'telefon', 'email', 'adresë'],
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-neutral-50">
      <ModernHeader />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-emerald-50 to-orange-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-primary font-bold mb-6">
              <span className="text-gradient-primary">Na</span>{' '}
              <span className="text-gradient-secondary">Kontaktoni</span>
            </h1>
            <p className="text-xl text-neutral-600 leading-relaxed">
              Jemi këtu për t'ju ndihmuar me çdo pyetje, këshillë apo mbështetje që mund t'ju duhet. 
              Ekipi ynë është gati të ju përgjigjet 24/7.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div className="animate-fade-in">
              <div className="card">
                <div className="card-body">
                  <h2 className="text-2xl font-primary font-bold mb-6">
                    Dërgo një <span className="text-gradient-primary">Mesazh</span>
                  </h2>
                  
                  <form className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Emri *
                        </label>
                        <input
                          type="text"
                          required
                          className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          placeholder="Emri juaj"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Mbiemri *
                        </label>
                        <input
                          type="text"
                          required
                          className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          placeholder="Mbiemri juaj"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        required
                        className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Telefoni
                      </label>
                      <input
                        type="tel"
                        className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        placeholder="+355 69 123 4567"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Subjekti *
                      </label>
                      <select
                        required
                        className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                      >
                        <option value="">Zgjidhni subjektin</option>
                        <option value="general">Pyetje të Përgjithshme</option>
                        <option value="order">Pyetje për Porosinë</option>
                        <option value="product">Informacion për Produktet</option>
                        <option value="veterinary">Konsulencë Veterinare</option>
                        <option value="complaint">Ankesë</option>
                        <option value="partnership">Partneritet</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Mesazhi *
                      </label>
                      <textarea
                        required
                        rows={6}
                        className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none"
                        placeholder="Shkruani mesazhin tuaj këtu..."
                      ></textarea>
                    </div>
                    
                    <button type="submit" className="btn btn-primary btn-lg w-full">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Dërgo Mesazhin
                    </button>
                  </form>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-8 animate-slide-in-right">
              {/* Contact Details */}
              <div className="card">
                <div className="card-body">
                  <h3 className="text-xl font-primary font-semibold mb-6">
                    Informacioni i <span className="text-gradient-primary">Kontaktit</span>
                  </h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-emerald-600 text-xl">📍</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-800 mb-1">Adresa</h4>
                        <p className="text-neutral-600">
                          Rruga e Durrësit, Nr. 123<br />
                          Tiranë 1001, Shqipëri
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-orange-600 text-xl">📞</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-800 mb-1">Telefoni</h4>
                        <p className="text-neutral-600">
                          +355 69 123 4567<br />
                          +355 4 123 4567
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-purple-600 text-xl">✉️</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-800 mb-1">Email</h4>
                        <p className="text-neutral-600">
                          <EMAIL><br />
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-emerald-600 text-xl">🕒</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-800 mb-1">Orari i Punës</h4>
                        <p className="text-neutral-600">
                          Hënë - Premte: 08:00 - 20:00<br />
                          Shtunë - Diel: 09:00 - 18:00
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* FAQ Quick Links */}
              <div className="card">
                <div className="card-body">
                  <h3 className="text-xl font-primary font-semibold mb-6">
                    Pyetje të <span className="text-gradient-primary">Shpeshta</span>
                  </h3>
                  
                  <div className="space-y-4">
                    {[
                      'Si mund të porosis online?',
                      'Cilat janë opsionet e pagesës?',
                      'Sa kohë merr transporti?',
                      'Si mund të kthej një produkt?',
                      'A ofron konsulencë veterinare?'
                    ].map((question, index) => (
                      <a
                        key={index}
                        href="/faq"
                        className="block p-3 rounded-lg border border-neutral-200 hover:border-emerald-300 hover:bg-emerald-50 transition-colors group"
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-neutral-700 group-hover:text-emerald-700">
                            {question}
                          </span>
                          <svg className="w-4 h-4 text-neutral-400 group-hover:text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              </div>

              {/* Social Media */}
              <div className="card">
                <div className="card-body">
                  <h3 className="text-xl font-primary font-semibold mb-6">
                    Na Ndiqni në <span className="text-gradient-primary">Rrjetet Sociale</span>
                  </h3>
                  
                  <div className="flex space-x-4">
                    <a href="#" className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-200 transition-colors">
                      <span className="text-blue-600 text-xl">📘</span>
                    </a>
                    <a href="#" className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center hover:bg-pink-200 transition-colors">
                      <span className="text-pink-600 text-xl">📷</span>
                    </a>
                    <a href="#" className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-200 transition-colors">
                      <span className="text-blue-600 text-xl">🐦</span>
                    </a>
                    <a href="#" className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center hover:bg-red-200 transition-colors">
                      <span className="text-red-600 text-xl">📺</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
