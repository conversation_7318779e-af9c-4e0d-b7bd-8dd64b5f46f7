'use client';

import React from 'react';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  badge?: string;
  isNew?: boolean;
  isOnSale?: boolean;
}

const FeaturedProducts: React.FC = () => {
  const products: Product[] = [
    {
      id: '1',
      name: 'Ushqim Premium për Qen - Royal Canin 15kg',
      price: 2200,
      originalPrice: 2500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 156,
      category: 'Ushqim për Qen',
      badge: 'Bestseller',
      isOnSale: true
    },
    {
      id: '2',
      name: 'Lodër Interaktive për Mace - SmartToy',
      price: 850,
      image: '/images/placeholder.svg',
      rating: 4.6,
      reviewCount: 89,
      category: 'Lodra për Mace',
      isNew: true
    },
    {
      id: '3',
      name: 'Akuarium Modern 100L me LED',
      price: 12000,
      originalPrice: 14000,
      image: '/images/placeholder.svg',
      rating: 4.9,
      reviewCount: 45,
      category: 'Akuarium',
      isOnSale: true
    },
    {
      id: '4',
      name: '<PERSON><PERSON><PERSON> për Zogj - <PERSON>',
      price: 4500,
      image: '/images/placeholder.svg',
      rating: 4.7,
      reviewCount: 67,
      category: 'Kafaze për Zogj',
      badge: 'Premium'
    },
    {
      id: '5',
      name: 'Set Aksesorësh për Qen - Complete Care',
      price: 1800,
      originalPrice: 2200,
      image: '/images/placeholder.svg',
      rating: 4.5,
      reviewCount: 123,
      category: 'Aksesorë për Qen',
      isOnSale: true
    },
    {
      id: '6',
      name: 'Shtrat Ortopedik për Mace - ComfortMax',
      price: 1500,
      image: '/images/placeholder.svg',
      rating: 4.8,
      reviewCount: 78,
      category: 'Shtretër për Mace',
      isNew: true
    }
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sq-AL', {
      style: 'currency',
      currency: 'ALL',
      minimumFractionDigits: 0
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-sm ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-neutral-300'}`}>
        ★
      </span>
    ));
  };

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-6 py-3 bg-primary-100 text-primary-700 rounded-full text-sm font-bold mb-8 border-2 border-primary-200">
            🔥 Produktet më të Populluara
          </div>
          <h2 className="text-4xl lg:text-5xl font-primary font-bold mb-8">
            Produktet e <span className="text-primary-600">Rekomanduara</span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            Zbulo produktet më të shitura dhe më të vlerësuara nga komuniteti ynë i dashuruesve të kafshëve.
            Çdo produkt është testuar dhe aprovuar nga ekspertët tanë veterinarë.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
          {products.map((product, index) => (
            <div
              key={product.id}
              className="card card-hover group"
            >
              {/* Product Image */}
              <div className="relative overflow-hidden rounded-t-xl">
                <div className="aspect-square bg-gradient-to-br from-neutral-100 to-neutral-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <div className="text-6xl opacity-50 group-hover:opacity-70 transition-opacity">📦</div>
                </div>

                {/* Enhanced Badges */}
                <div className="absolute top-4 left-4 flex flex-col gap-2">
                  {product.isNew && (
                    <span className="px-3 py-1 bg-primary-400 text-neutral-900 text-xs font-bold rounded-full shadow-md">
                      🆕 E Re
                    </span>
                  )}
                  {product.isOnSale && (
                    <span className="px-3 py-1 bg-red-500 text-white text-xs font-bold rounded-full shadow-md">
                      🔥 Ofertë
                    </span>
                  )}
                  {product.badge && (
                    <span className="px-3 py-1 bg-blue-600 text-white text-xs font-bold rounded-full shadow-md">
                      ⭐ {product.badge}
                    </span>
                  )}
                </div>

                {/* Enhanced Quick Actions */}
                <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                  <button className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-red-50 hover:scale-110 transition-all border-2 border-transparent hover:border-red-200">
                    <svg className="w-5 h-5 text-neutral-600 hover:text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </button>
                  <button className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-50 hover:scale-110 transition-all border-2 border-transparent hover:border-blue-200">
                    <svg className="w-5 h-5 text-neutral-600 hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button className="w-12 h-12 bg-primary-400 rounded-full shadow-lg flex items-center justify-center hover:bg-primary-500 hover:scale-110 transition-all text-neutral-900">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="card-body">
                <div className="text-xs sm:text-sm text-neutral-500 mb-2 font-medium">{product.category}</div>
                <h3 className="font-primary font-bold text-neutral-800 mb-3 group-hover:text-yellow-600 transition-colors line-clamp-2 text-base sm:text-lg">
                  {product.name}
                </h3>

                {/* Enhanced Rating */}
                <div className="flex items-center gap-2 sm:gap-3 mb-4">
                  <div className="flex">{renderStars(product.rating)}</div>
                  <span className="text-xs sm:text-sm text-neutral-500 font-medium">({product.reviewCount})</span>
                </div>

                {/* Enhanced Price */}
                <div className="flex items-start justify-between mb-4 sm:mb-6">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="text-xl sm:text-2xl font-bold text-neutral-800">
                        {formatPrice(product.price)}
                      </span>
                      {product.originalPrice && (
                        <span className="text-xs sm:text-sm text-neutral-500 line-through">
                          {formatPrice(product.originalPrice)}
                        </span>
                      )}
                    </div>
                    {product.originalPrice && (
                      <span className="text-xs text-green-600 font-medium mt-1">
                        Kursim: {formatPrice(product.originalPrice - product.price)}
                      </span>
                    )}
                  </div>
                  {product.originalPrice && (
                    <span className="text-xs sm:text-sm font-bold text-red-600 bg-red-50 px-2 py-1 rounded-full">
                      -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                    </span>
                  )}
                </div>

                {/* Enhanced Add to Cart Button */}
                <button className="btn btn-primary w-full group-hover:scale-105 transition-transform text-sm sm:text-base">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                  </svg>
                  Shto në Shportë
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* View All Products Button */}
        <div className="text-center">
          <Link href="/products" className="btn btn-outline btn-lg">
            Shiko të Gjitha Produktet
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
