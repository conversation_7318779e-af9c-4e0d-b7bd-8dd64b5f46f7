<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PawLux - Premium Pet Store</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d3436;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 224, 3, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 224, 3, 0.3);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: #2d3436;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: #ffe003;
            transform: translateY(-2px);
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .cart-btn {
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            color: #2d3436;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 224, 3, 0.3);
            transform: perspective(1000px) rotateX(0deg);
        }

        .cart-btn:hover {
            transform: perspective(1000px) rotateX(-10deg) translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 224, 3, 0.4);
        }

        /* Hero Section */
        .hero {
            margin-top: 80px;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,224,3,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,224,3,0.15)"/><circle cx="40" cy="80" r="1" fill="rgba(255,224,3,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100px); }
        }

        .hero-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .hero-text .highlight {
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-text p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .cta-btn {
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            color: #2d3436;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 224, 3, 0.3);
            transform: perspective(1000px) rotateX(0deg);
        }

        .cta-btn:hover {
            transform: perspective(1000px) rotateX(-15deg) translateY(-8px);
            box-shadow: 0 20px 40px rgba(255, 224, 3, 0.4);
        }

        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .floating-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
            animation: floatCard 6s ease-in-out infinite;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        @keyframes floatCard {
            0%, 100% { transform: perspective(1000px) rotateY(-15deg) rotateX(10deg) translateY(0px); }
            50% { transform: perspective(1000px) rotateY(-15deg) rotateX(10deg) translateY(-20px); }
        }

        .floating-card h3 {
            color: #ffe003;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .floating-card p {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
        }

        /* Products Section */
        .products {
            padding: 6rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 1rem;
        }

        .section-header .highlight {
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-header p {
            font-size: 1.1rem;
            color: #636e72;
            max-width: 600px;
            margin: 0 auto;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            transform: perspective(1000px) rotateX(0deg);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .product-card:hover::before {
            transform: translateX(0);
        }

        .product-card:hover {
            transform: perspective(1000px) rotateX(-5deg) translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 200px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border-radius: 15px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 224, 3, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
        }

        .product-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 0.5rem;
        }

        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffe003;
            margin-bottom: 1rem;
        }

        .product-description {
            color: #636e72;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .add-to-cart {
            width: 100%;
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: white;
            border: none;
            padding: 0.8rem;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-to-cart:hover {
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            color: #2d3436;
            transform: translateY(-2px);
        }

        /* Features Section */
        .features {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            padding: 6rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><polygon points="100,10 40,180 190,60 10,60 160,180" fill="rgba(255,224,3,0.03)"/></svg>') repeat;
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features-content {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .features h2 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 4rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            transform: perspective(1000px) rotateX(0deg);
        }

        .feature-card:hover {
            transform: perspective(1000px) rotateX(-10deg) translateY(-10px);
            background: rgba(255, 224, 3, 0.1);
            border-color: rgba(255, 224, 3, 0.3);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffe003 0%, #ffb300 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            box-shadow: 0 10px 30px rgba(255, 224, 3, 0.3);
        }

        .feature-card h3 {
            color: white;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: #2d3436;
            color: white;
            padding: 3rem 2rem 1rem;
            text-align: center;
        }

        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
        }

        .footer p {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                padding: 1rem;
            }

            .nav-links {
                display: none;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🐾 PawLux</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#products">Products</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="nav-actions">
                <button class="cart-btn">🛒 Cart (0)</button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Premium <span class="highlight">Pet Care</span> Products</h1>
                <p>Discover our curated collection of high-quality pet supplies, toys, and accessories designed to keep your furry friends happy and healthy.</p>
                <button class="cta-btn">Shop Now</button>
            </div>
            <div class="hero-visual">
                <div class="floating-card">
                    <h3>🌟 Featured Today</h3>
                    <p>Premium organic pet food with 30% off for new customers. Give your pets the nutrition they deserve.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products" id="products">
        <div class="section-header">
            <h2>Featured <span class="highlight">Products</span></h2>
            <p>Carefully selected products that your pets will love, from premium food to engaging toys.</p>
        </div>
        <div class="product-grid">
            <div class="product-card">
                <div class="product-image">🍖</div>
                <h3 class="product-title">Premium Dog Food</h3>
                <div class="product-price">$49.99</div>
                <p class="product-description">Organic, grain-free formula with real meat as the first ingredient. Perfect for all life stages.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
            <div class="product-card">
                <div class="product-image">🧸</div>
                <h3 class="product-title">Interactive Plush Toy</h3>
                <div class="product-price">$24.99</div>
                <p class="product-description">Durable, squeaky toy that keeps your pet entertained for hours. Machine washable and safe.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
            <div class="product-card">
                <div class="product-image">🐱</div>
                <h3 class="product-title">Cat Scratching Tower</h3>
                <div class="product-price">$89.99</div>
                <p class="product-description">Multi-level scratching post with sisal rope and cozy hideouts. Perfect for active cats.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
            <div class="product-card">
                <div class="product-image">🦴</div>
                <h3 class="product-title">Natural Chew Bones</h3>
                <div class="product-price">$19.99</div>
                <p class="product-description">Long-lasting, natural bones that promote dental health and satisfy your dog's chewing instincts.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
            <div class="product-card">
                <div class="product-image">🏠</div>
                <h3 class="product-title">Cozy Pet Bed</h3>
                <div class="product-price">$69.99</div>
                <p class="product-description">Memory foam bed with removable, washable cover. Provides ultimate comfort for your pet.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
            <div class="product-card">
                <div class="product-image">🎾</div>
                <h3 class="product-title">Fetch Ball Set</h3>
                <div class="product-price">$14.99</div>
                <p class="product-description">Set of 3 rubber balls perfect for fetch games. Bouncy, durable, and easy to clean.</p>
                <button class="add-to-cart">Add to Cart</button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="features-content">
            <h2>Why Choose <span style="color: #ffe003;">PawLux</span>?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚚</div>
                    <h3>Free Shipping</h3>
                    <p>Free delivery on orders over $50. Fast, reliable shipping to your doorstep within 2-3 business days.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <h3>Premium Quality</h3>
                    <p>All products are carefully selected and tested to ensure the highest quality standards for your pets.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💝</div>
                    <h3>Satisfaction Guarantee</h3>
                    <p>30-day money-back guarantee. If you're not satisfied, we'll make it right or refund your purchase.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🩺</div>
                    <h3>Vet Approved</h3>
                    <p>Our products are recommended by veterinarians and pet care professionals across the country.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2025 PawLux. All rights reserved. Made with ❤️ for pets and their humans.</p>
        </div>
    </footer>

    <script>
        // Add smooth scrolling and interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Add to cart functionality
            let cartCount = 0;
            const cartBtn = document.querySelector('.cart-btn');
            const addToCartBtns = document.querySelectorAll('.add-to-cart');

            addToCartBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    cartCount++;
                    cartBtn.textContent = `🛒 Cart (${cartCount})`;
                    
                    // Add success animation
                    this.style.transform = 'scale(0.95)';
                    this.textContent = 'Added!';
                    this.style.background = 'linear-gradient(135deg, #00b894 0%, #00a085 100%)';
                    
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                        this.textContent = 'Add to Cart';
                        this.style.background = '';
                    }, 1000);
                });
            });

            // Header scroll effect
            window.addEventListener('scroll', function() {
                const header = document.querySelector('.header');
                if (window.scrollY > 100) {
                    header.style.background = 'rgba(255, 255, 255, 0.98)';
                    header.style.boxShadow = '0 5px 30px rgba(0, 0, 0, 0.1)';
                } else {
                    header.style.background = 'rgba(255, 255, 255, 0.95)';
                    header.style.boxShadow = 'none';
                }
            });
        });
    </script>
</body>
</html>