'use client';

import React from 'react';

const TrustBadges: React.FC = () => {
  const badges = [
    {
      icon: '🔒',
      title: 'Pagesa e Sigurt',
      description: 'SSL 256-bit enkriptim'
    },
    {
      icon: '🚚',
      title: 'Transport Falas',
      description: 'Mbi 5000 Lekë'
    },
    {
      icon: '↩️',
      title: '<PERSON><PERSON><PERSON>',
      description: '30 ditë garanci'
    },
    {
      icon: '🏆',
      title: 'Cilësi e Garantuar',
      description: 'Produkte origjinale'
    },
    {
      icon: '📞',
      title: 'Mbështetje 24/7',
      description: 'Ekspertë veterinarë'
    }
  ];

  return (
    <div className="bg-white border-t border-neutral-200 py-8">
      <div className="container-custom">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {badges.map((badge, index) => (
            <div key={index} className="text-center group">
              <div className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                  <span className="text-2xl">{badge.icon}</span>
                </div>
                <div>
                  <h4 className="text-sm font-bold text-neutral-800 group-hover:text-primary-600 transition-colors">
                    {badge.title}
                  </h4>
                  <p className="text-xs text-neutral-500 mt-1">
                    {badge.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrustBadges;
