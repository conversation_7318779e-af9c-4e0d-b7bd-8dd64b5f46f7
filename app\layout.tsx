import type { <PERSON>ada<PERSON>, Viewport } from "next";
import "./globals.css";

export const metadata: Metadata = {
  metadataBase: new URL('https://petshop-albania.com'),
  title: {
    default: "PetShop Albania - Dyqani Online për Kafshë dhe Aksesorë",
    template: "%s | PetShop Albania"
  },
  description: "Dyqani më i madh online për produkte të kafshëve në Shqipëri. Ushqim, lodra, aksesorë dhe produkte veterinare për qen, mace, zogj dhe kafshë të tjera. Transport falas mbi 5000 Lekë.",
  keywords: [
    "petshop albania", "dyqan kafshësh", "ushqim qeni", "ushqim mace",
    "akses<PERSON><PERSON> kafsh<PERSON>sh", "lodra kafshësh", "produkte veterinare",
    "transport falas", "online pet store", "pet accessories albania"
  ],
  authors: [{ name: "PetShop Albania" }],
  creator: "PetShop Albania",
  publisher: "PetShop Albania",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/apple-touch-icon.png",
  },
  openGraph: {
    type: "website",
    locale: "sq_AL",
    url: "https://petshop-albania.com",
    title: "PetShop Albania - Dyqani Online për Kafshë",
    description: "Dyqani më i madh online për produkte të kafshëve në Shqipëri. Transport falas mbi 5000 Lekë.",
    siteName: "PetShop Albania",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "PetShop Albania - Dyqani Online për Kafshë",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "PetShop Albania - Dyqani Online për Kafshë",
    description: "Dyqani më i madh online për produkte të kafshëve në Shqipëri.",
    images: ["/og-image.jpg"],
    creator: "@petshopalbania",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
};

export const viewport: Viewport = {
  themeColor: "#10B981",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

const structuredData = {
  "@context": "https://schema.org",
  "@type": "PetStore",
  "name": "PetShop Albania",
  "description": "Dyqani më i madh online për produkte të kafshëve në Shqipëri",
  "url": "https://petshop-albania.com",
  "logo": "https://petshop-albania.com/logo.png",
  "image": "https://petshop-albania.com/og-image.jpg",
  "telephone": "+355-69-123-4567",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Rruga e Durrësit",
    "addressLocality": "Tiranë",
    "addressCountry": "AL"
  },
  "openingHours": "Mo-Su 08:00-20:00",
  "priceRange": "$$",
  "acceptsReservations": false,
  "servesCuisine": "Pet Food",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Pet Products",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Product",
          "name": "Ushqim për Qen",
          "category": "Pet Food"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Product",
          "name": "Aksesorë për Kafshë",
          "category": "Pet Accessories"
        }
      }
    ]
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="sq" className="scroll-smooth">
      <head>
        <link rel="manifest" href="/manifest.json" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className="antialiased">
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  );
}
