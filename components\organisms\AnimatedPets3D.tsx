'use client';

import React from 'react';

const AnimatedPets3D: React.FC = () => {
  return (
    <div className="relative h-80 flex items-center justify-center perspective-1000">
      {/* Dog - 3D Floating Animation */}
      <div 
        className="absolute text-7xl animate-float-3d transform-3d" 
        style={{ 
          animationDelay: '0s', 
          top: '20%', 
          left: '20%',
          transform: 'translate(-50%, -50%)'
        }}
      >
        <span className="drop-shadow-xl">🐕‍🦺</span>
      </div>
      
      {/* Cat - 3D Floating Animation */}
      <div 
        className="absolute text-6xl animate-float-3d transform-3d" 
        style={{ 
          animationDelay: '0.5s', 
          top: '30%', 
          right: '25%',
          transform: 'translate(50%, -50%)'
        }}
      >
        <span className="drop-shadow-xl">🐱</span>
      </div>
      
      {/* Bird - 3D Floating Animation */}
      <div 
        className="absolute text-5xl animate-float-3d transform-3d" 
        style={{ 
          animationDelay: '1s', 
          bottom: '30%', 
          left: '25%',
          transform: 'translate(-50%, 50%)'
        }}
      >
        <span className="drop-shadow-xl">🐦</span>
      </div>
      
      {/* Fish - 3D Floating Animation */}
      <div 
        className="absolute text-4xl animate-float-3d transform-3d" 
        style={{ 
          animationDelay: '1.5s', 
          bottom: '25%', 
          right: '20%',
          transform: 'translate(50%, 50%)'
        }}
      >
        <span className="drop-shadow-xl">🐠</span>
      </div>
      
      {/* Central Heart - 3D Effect */}
      <div 
        className="absolute text-5xl animate-pulse transform-3d" 
        style={{ 
          top: '50%', 
          left: '50%',
          transform: 'translate(-50%, -50%)'
        }}
      >
        <span className="drop-shadow-xl text-red-500">❤️</span>
      </div>
    </div>
  );
};

export default AnimatedPets3D;
