'use client';

import React, { useState } from 'react';
import Button from '@/components/atoms/Button';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-primary-coral">
              PetShop<span className="text-primary-teal">AL</span>
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Ballina</a>
            <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Produktet</a>
            <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Kategoritë</a>
            <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Rreth Nesh</a>
            <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Kontakt</a>
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            <button className="text-neutral-dark hover:text-primary-coral">
              <span className="sr-only">Kërko</span>
              🔍
            </button>
            <button className="text-neutral-dark hover:text-primary-coral">
              <span className="sr-only">Përdoruesi</span>
              👤
            </button>
            <button 
              className="relative text-neutral-dark hover:text-primary-coral"
              onClick={() => setIsCartOpen(!isCartOpen)}
            >
              <span className="sr-only">Shporta</span>
              🛒
              <span className="absolute -top-2 -right-2 bg-primary-coral text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                3
              </span>
            </button>
            
            {/* Mobile menu button */}
            <button 
              className="md:hidden text-neutral-dark hover:text-primary-coral"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <span className="sr-only">Menu</span>
              {isMenuOpen ? '✕' : '☰'}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-neutral-light">
            <nav className="flex flex-col space-y-3">
              <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Ballina</a>
              <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Produktet</a>
              <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Kategoritë</a>
              <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Rreth Nesh</a>
              <a href="#" className="text-neutral-dark hover:text-primary-coral font-medium">Kontakt</a>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
