// Simple test script to verify the application structure
const fs = require('fs');
const path = require('path');

// Check if essential files exist
const essentialFiles = [
  'app/page.tsx',
  'app/layout.tsx',
  'app/products/page.tsx',
  'app/cart/page.tsx',
  'app/checkout/page.tsx',
  'components/atoms/Button.tsx',
  'components/molecules/ProductCard.tsx',
  'components/organisms/Header.tsx',
  'components/organisms/HeroSection.tsx',
  'components/organisms/CategoryShowcase.tsx',
  'public/manifest.json',
  'public/sw.js',
  'public/offline.html'
];

console.log('Testing PetShop Albania application structure...\n');

let allFilesExist = true;

for (const file of essentialFiles) {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✓ ${file} exists`);
  } else {
    console.log(`✗ ${file} is missing`);
    allFilesExist = false;
  }
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('✓ All essential files are present');
  console.log('✓ Application structure is complete');
  console.log('\nTo run the application:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Start development server: npm run dev');
  console.log('3. Open http://localhost:3000 in your browser');
} else {
  console.log('✗ Some essential files are missing');
  console.log('✗ Please check the application structure');
}

console.log('\n' + '='.repeat(50));
