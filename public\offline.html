<!DOCTYPE html>
<html lang="sq">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PetShop Albania - Jash<PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #4ECDC4 0%, #95E1D3 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #2D3436;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            max-width: 90%;
            width: 500px;
        }
        
        h1 {
            color: #FF6B6B;
            margin-bottom: 1rem;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .paw-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #FF6B6B 0%, #FFE66D 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="paw-icon">🐾</div>
        <h1>Jashtë Linje</h1>
        <p>Duket se nuk jeni të lidhur me internetin. Ju lutemi kontrolloni lidhjen tuaj dhe provoni përsëri.</p>
        <button class="retry-btn" onclick="retryConnection()">Provo Përsëri</button>
    </div>

    <script>
        function retryConnection() {
            window.location.reload();
        }
        
        // Check for online status
        window.addEventListener('online', () => {
            window.location.href = '/';
        });
    </script>
</body>
</html>
