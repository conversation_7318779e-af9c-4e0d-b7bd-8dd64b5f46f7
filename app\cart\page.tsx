'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ModernHeader from '@/components/organisms/ModernHeader';

interface CartItem {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  quantity: number;
  category: string;
}

export default function CartPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: '1',
      name: 'Ushqim Premium për Qen - Royal Canin 15kg',
      price: 2200,
      originalPrice: 2500,
      image: '/images/placeholder.svg',
      quantity: 1,
      category: 'Ushqim për Qen'
    },
    {
      id: '2',
      name: 'Lodër Interaktive për Mace - SmartToy',
      price: 850,
      image: '/images/placeholder.svg',
      quantity: 2,
      category: 'Lodra për Mace'
    },
    {
      id: '3',
      name: 'Shtrat Ortopedik për Qen - ComfortMax',
      price: 1500,
      image: '/images/placeholder.svg',
      quantity: 1,
      category: 'Shtretër për Qen'
    }
  ]);

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity === 0) {
      setCartItems(cartItems.filter(item => item.id !== id));
    } else {
      setCartItems(cartItems.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      ));
    }
  };

  const removeItem = (id: string) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = subtotal >= 5000 ? 0 : 500;
  const total = subtotal + shipping;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sq-AL', {
      style: 'currency',
      currency: 'ALL',
      minimumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="min-h-screen bg-neutral-50">
      <ModernHeader />
      
      <main className="container-custom py-8 mt-20">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm text-neutral-500">
          <Link href="/" className="hover:text-primary-600 transition-colors">Ballina</Link>
          <span className="mx-2">/</span>
          <span className="text-neutral-800 font-medium">Shporta</span>
        </nav>
        
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl lg:text-4xl font-primary font-bold text-neutral-800 mb-4">
            Shporta Juaj
          </h1>
          <p className="text-lg text-neutral-600">
            {cartItems.length} {cartItems.length === 1 ? 'produkt' : 'produkte'} në shportë
          </p>
        </div>

        {cartItems.length === 0 ? (
          /* Empty Cart */
          <div className="text-center py-16">
            <div className="text-6xl mb-6">🛒</div>
            <h2 className="text-2xl font-bold text-neutral-800 mb-4">Shporta është bosh</h2>
            <p className="text-neutral-600 mb-8">Shtoni produkte në shportë për të vazhduar me blerjen.</p>
            <Link href="/products" className="btn btn-primary btn-lg">
              Vazhdoni Blerjen
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-6">
              {cartItems.map((item) => (
                <div key={item.id} className="card">
                  <div className="card-body">
                    <div className="flex flex-col sm:flex-row gap-4">
                      {/* Product Image */}
                      <div className="w-full sm:w-24 h-24 bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-lg flex items-center justify-center flex-shrink-0">
                        <div className="text-3xl opacity-50">📦</div>
                      </div>
                      
                      {/* Product Info */}
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                          <div>
                            <h3 className="font-semibold text-neutral-800 mb-2">{item.name}</h3>
                            <p className="text-sm text-neutral-500 mb-2">{item.category}</p>
                            <div className="flex items-center gap-2">
                              <span className="text-lg font-bold text-neutral-800">
                                {formatPrice(item.price)}
                              </span>
                              {item.originalPrice && (
                                <span className="text-sm text-neutral-500 line-through">
                                  {formatPrice(item.originalPrice)}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          {/* Quantity Controls */}
                          <div className="flex items-center gap-4">
                            <div className="flex items-center border border-neutral-200 rounded-lg">
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                className="w-10 h-10 flex items-center justify-center hover:bg-neutral-100 transition-colors"
                              >
                                -
                              </button>
                              <span className="w-12 text-center font-medium">{item.quantity}</span>
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                className="w-10 h-10 flex items-center justify-center hover:bg-neutral-100 transition-colors"
                              >
                                +
                              </button>
                            </div>
                            
                            <button
                              onClick={() => removeItem(item.id)}
                              className="text-red-500 hover:text-red-700 transition-colors p-2"
                              title="Hiq nga shporta"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="card sticky top-24">
                <div className="card-header">
                  <h2 className="text-xl font-bold text-neutral-800">Përmbledhja e Porosisë</h2>
                </div>
                
                <div className="card-body space-y-4">
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Nëntotali:</span>
                    <span className="font-medium">{formatPrice(subtotal)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Transport:</span>
                    <span className={`font-medium ${shipping === 0 ? 'text-green-600' : ''}`}>
                      {shipping === 0 ? 'FALAS' : formatPrice(shipping)}
                    </span>
                  </div>
                  
                  {shipping > 0 && (
                    <div className="text-sm text-neutral-500 bg-neutral-100 p-3 rounded-lg">
                      Shtoni {formatPrice(5000 - subtotal)} për transport falas
                    </div>
                  )}
                  
                  <div className="border-t border-neutral-200 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-neutral-800">Totali:</span>
                      <span className="text-xl font-bold text-primary-600">{formatPrice(total)}</span>
                    </div>
                  </div>
                  
                  <Link href="/checkout" className="btn btn-primary w-full btn-lg">
                    Vazhdoni me Pagesën
                  </Link>
                  
                  <Link href="/products" className="btn btn-outline w-full">
                    Vazhdoni Blerjen
                  </Link>
                </div>
                
                <div className="card-footer">
                  <div className="flex items-center justify-center text-sm text-neutral-500">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Pagesa 100% e sigurt
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
