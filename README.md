# PetShop Albania - E-Commerce Platform

Dyqani më i madh online për produkte të kafshëve në Shqipëri.

## Përshkrimi

Ky projekt është një platformë e-commerce për shitjen e produkteve për kafshë në Shqipëri. Platforma ofron një përvojë blerjeje të paharrueshme për pronarët e kafshëve me një ndërfaqe të përdoruesit moderne dhe të lehtë për tu përdorur.

## Karakteristikat

- Katalog i plotë i produkteve për kafshë të ndryshme (qen, mace, zogj, peshq, kafshë të vogla)
- Sistem i avancuar filtrimi dhe kërkimit
- Shportë blerjeje dhe proces pagese i sigurt
- Profilizim i produkteve bazuar në llojin e kafshës
- Sistem vlerësimesh dhe komente nga klientët
- Program besnikërie dhe oferta speciale

## Teknologjitë

- [Next.js](https://nextjs.org/) - Framework React për aplikacione web
- [TypeScript](https://www.typescriptlang.org/) - JavaScript me sintaksë për tipa
- [Tailwind CSS](https://tailwindcss.com/) - Framework CSS për stilizim
- [Framer Motion](https://www.framer.com/motion/) - Bibliotekë animacionesh

## Struktura e Projektit

```
.
├── app/                    # Faqet dhe layout-i i Next.js
├── components/             # Komponentët e aplikacionit
│   ├── atoms/             # Komponentët baze
│   ├── molecules/         # Komponentët e përbërë
│   ├── organisms/         # Komponentët kompleks
│   └── templates/         # Template për faqet
├── public/                 # Dosja për assets statik
└── styles/                 # Stilet globale
```

## Komponentët Kryesorë

### Atoms
- Button - Butonat e përdorur në të gjithë aplikacionin
- Input - Fushat e inputit për formularët

### Molecules
- ProductCard - Kartela e produktit me informacion bazë
- CartItem - Elementi i shportës së blerjeve

### Organisms
- Header - Menyja dhe navigimi kryesor
- HeroSection - Seksioni kryesor i faqes së parë
- CategoryShowcase - Shfaqja e kategorive të produkteve
- Footer - Fundi i faqes me informacion kontakti

## Paleta e Ngjyrave

- Primary Coral: #FF6B6B
- Primary Teal: #4ECDC4
- Primary Golden: #FFE66D
- Secondary Purple: #A8E6CF
- Secondary Mint: #95E1D3
- Secondary Peach: #FFAAA5

## Instalimi

1. Klononi repositorinë:
   ```bash
   git clone https://github.com/your-username/pet-ecommerce-albania.git
   ```

2. Navigoni në direktorinë e projektit:
   ```bash
   cd pet-ecommerce-albania
   ```

3. Instaloni dependencat:
   ```bash
   npm install
   ```

4. Nisni serverin në zhvillim:
   ```bash
   npm run dev
   ```

5. Hapni http://localhost:3000 në shfletuesin tuaj.

## Zhvillimi

Për të filluar zhvillimin, ndiqni këto hapa:

1. Krijoni një branch të ri:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Bëni ndryshimet tuaja

3. Bëni commit ndryshimet:
   ```bash
   git commit -m "Add your feature description"
   ```

4. Push branch-in tuaj:
   ```bash
   git push origin feature/your-feature-name
   ```

5. Hapni një Pull Request

## Licenca

Ky projekt është i licencuar sipas licencës MIT - shihni skedarin [LICENSE](LICENSE) për më shumë detaje.

## Kontakt

Për pyetje ose mbështetje, kontaktoni ne në <EMAIL>.
