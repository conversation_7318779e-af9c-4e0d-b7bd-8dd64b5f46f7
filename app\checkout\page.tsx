'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ModernHeader from '@/components/organisms/ModernHeader';

export default function CheckoutPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    paymentMethod: 'card'
  });

  const steps = [
    { id: 1, name: 'Informacioni', icon: '📝' },
    { id: 2, name: 'Pagesa', icon: '💳' },
    { id: 3, name: 'Konfirm<PERSON><PERSON>', icon: '✅' }
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      <ModernHeader />

      <main className="container-custom py-8 mt-20">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm text-neutral-500">
          <Link href="/" className="hover:text-primary-600 transition-colors">Ballina</Link>
          <span className="mx-2">/</span>
          <Link href="/cart" className="hover:text-primary-600 transition-colors">Shporta</Link>
          <span className="mx-2">/</span>
          <span className="text-neutral-800 font-medium">Pagesa</span>
        </nav>

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl lg:text-4xl font-primary font-bold text-neutral-800 mb-4">Pagesa</h1>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-colors ${
                  currentStep >= step.id
                    ? 'bg-primary-400 border-primary-400 text-neutral-900'
                    : 'bg-white border-neutral-300 text-neutral-500'
                }`}>
                  <span className="text-lg">{step.icon}</span>
                </div>
                <div className="ml-3 mr-6">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-primary-600' : 'text-neutral-500'
                  }`}>
                    {step.name}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mr-6 ${
                    currentStep > step.id ? 'bg-primary-400' : 'bg-neutral-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-xl font-bold text-neutral-800">Informacioni i Kontaktit</h2>
              </div>
              <div className="card-body">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Emri *
                    </label>
                    <input
                      type="text"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="Emri juaj"
                    />
                  </div>

                  <div>
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Mbiemri *
                    </label>
                    <input
                      type="text"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="Mbiemri juaj"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Numri i Telefonit *
                    </label>
                    <input
                      type="tel"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="+355 XX XXX XXXX"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Shipping Address */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-xl font-bold text-neutral-800">Adresa e Dërgesës</h2>
              </div>
              <div className="card-body">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Adresa *
                    </label>
                    <input
                      type="text"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="Rruga, numri i banesës, apartamenti"
                    />
                  </div>

                  <div>
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Qyteti *
                    </label>
                    <select className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors">
                      <option>Zgjidhni qytetin</option>
                      <option>Tiranë</option>
                      <option>Durrës</option>
                      <option>Vlorë</option>
                      <option>Shkodër</option>
                      <option>Elbasan</option>
                      <option>Korçë</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-neutral-600 text-sm font-medium mb-2">
                      Kodi Postar
                    </label>
                    <input
                      type="text"
                      className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                      placeholder="1001"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* Payment Method */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-xl font-bold text-neutral-800">Metoda e Pagesës</h2>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <label className="flex items-center p-4 border-2 border-neutral-200 rounded-lg cursor-pointer hover:border-primary-400 transition-colors">
                    <input type="radio" name="payment" className="h-5 w-5 text-primary-600 focus:ring-primary-400" defaultChecked />
                    <div className="ml-4 flex-1">
                      <span className="font-medium text-neutral-800">Pagesa me Kartë Krediti</span>
                      <p className="text-neutral-600 text-sm mt-1">Visa, Mastercard, American Express</p>
                    </div>
                    <div className="text-2xl">💳</div>
                  </label>

                  <label className="flex items-center p-4 border-2 border-neutral-200 rounded-lg cursor-pointer hover:border-primary-400 transition-colors">
                    <input type="radio" name="payment" className="h-5 w-5 text-primary-600 focus:ring-primary-400" />
                    <div className="ml-4 flex-1">
                      <span className="font-medium text-neutral-800">Pagesa në Dorë</span>
                      <p className="text-neutral-600 text-sm mt-1">Paguani kur merrni porosinë</p>
                    </div>
                    <div className="text-2xl">💰</div>
                  </label>
                </div>

                {/* Card Details */}
                <div className="mt-6 p-6 bg-neutral-50 rounded-lg border border-neutral-200">
                  <h3 className="font-medium text-neutral-800 mb-4">Detajet e Kartës</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-neutral-600 text-sm font-medium mb-2">
                        Numri i Kartës *
                      </label>
                      <input
                        type="text"
                        className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                        placeholder="1234 5678 9012 3456"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-neutral-600 text-sm font-medium mb-2">
                          Data e Skadimit *
                        </label>
                        <input
                          type="text"
                          className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                          placeholder="MM/YY"
                        />
                      </div>

                      <div>
                        <label className="block text-neutral-600 text-sm font-medium mb-2">
                          CVC *
                        </label>
                        <input
                          type="text"
                          className="w-full border border-neutral-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-colors"
                          placeholder="123"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="card sticky top-24">
              <div className="card-header">
                <h2 className="text-xl font-bold text-neutral-800">Përmbledhja e Porosisë</h2>
              </div>

              <div className="card-body">
                {/* Order Items */}
                <div className="space-y-4 mb-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <span className="text-neutral-800 font-medium">Ushqim Premium për Qen</span>
                      <div className="text-sm text-neutral-500">Royal Canin 15kg × 1</div>
                    </div>
                    <span className="text-neutral-800 font-medium">2,200 Lekë</span>
                  </div>

                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <span className="text-neutral-800 font-medium">Lodër Interaktive për Mace</span>
                      <div className="text-sm text-neutral-500">SmartToy × 2</div>
                    </div>
                    <span className="text-neutral-800 font-medium">1,700 Lekë</span>
                  </div>

                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <span className="text-neutral-800 font-medium">Shtrat Ortopedik për Qen</span>
                      <div className="text-sm text-neutral-500">ComfortMax × 1</div>
                    </div>
                    <span className="text-neutral-800 font-medium">1,500 Lekë</span>
                  </div>
                </div>

                {/* Totals */}
                <div className="space-y-3 pt-4 border-t border-neutral-200">
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Nëntotali:</span>
                    <span className="text-neutral-800 font-medium">5,400 Lekë</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-neutral-600">Transport:</span>
                    <span className="text-green-600 font-medium">FALAS</span>
                  </div>

                  <div className="flex justify-between pt-3 border-t border-neutral-200">
                    <span className="text-lg font-bold text-neutral-800">Totali:</span>
                    <span className="text-xl font-bold text-primary-600">5,400 Lekë</span>
                  </div>
                </div>

                <button className="btn btn-primary w-full btn-lg mt-6">
                  Përfundo Porosinë
                </button>

                <div className="mt-6 pt-6 border-t border-neutral-200">
                  <div className="flex items-center justify-center text-sm text-neutral-500">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span>Pagesa 100% e sigurt</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
